make -f /root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile --dry-run --always-make --keep-going --print-directory
make: Entering directory `/root/q/t3_scu_hebing_V2_DKY'
make: Leaving directory `/root/q/t3_scu_hebing_V2_DKY'
 
make: /root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile: No such file or directory
make: *** No rule to make target `/root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile'.
make: Failed to remake makefile `/root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile'.
make: *** No targets specified and no makefile found.  Stop.

