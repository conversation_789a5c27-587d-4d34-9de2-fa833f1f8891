make all -f /root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile --print-data-base --no-builtin-variables --no-builtin-rules --question
# GNU Make 3.82
# Built for x86_64-redhat-linux-gnu
# Copyright (C) 2010  Free Software Foundation, Inc.
# License GPLv3+: GNU GPL version 3 or later <http://gnu.org/licenses/gpl.html>
# This is free software: you are free to change and redistribute it.
# There is NO WARRANTY, to the extent permitted by law.
 
make: /root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile: No such file or directory
make: *** No rule to make target `/root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile'.  Stop.


# Make data base, printed on Mon Aug 11 22:42:13 2025

# Variables

# automatic
<D = $(patsubst %/,%,$(dir $<))
# automatic
?F = $(notdir $?)
# default
.SHELLFLAGS := -c
# environment
VSCODE_AGENT_FOLDER = /root/.vscode-server
# automatic
?D = $(patsubst %/,%,$(dir $?))
# automatic
@D = $(patsubst %/,%,$(dir $@))
# automatic
@F = $(notdir $@)
# default
MAKE_VERSION := 3.82
# makefile
CURDIR := /root/q/t3_scu_hebing_V2_DKY
# makefile
SHELL = /bin/sh
# environment
VSCODE_NLS_CONFIG = {"userLocale":"zh-cn","osLocale":"zh-cn","resolvedLanguage":"en","defaultMessagesFile":"/root/.vscode-server/cli/servers/Stable-ddc367ed5c8936efe395cffeec279b04ffd7db78/server/out/nls.messages.json","locale":"zh-cn","availableLanguages":{}}
# environment
_ = /usr/bin/make
# makefile
MAKEFILE_LIST := 
# environment
HISTCONTROL = ignoredups
# environment
XDG_DATA_DIRS = /root/.local/share/flatpak/exports/share:/var/lib/flatpak/exports/share:/usr/local/share:/usr/share
# environment
LESSOPEN = ||/usr/bin/lesspipe.sh %s
# environment
VSCODE_CWD = /root
# environment
SSH_CONNECTION = ************* 53334 *************92 22
# environment
PATH = /root/.vscode-server/cli/servers/Stable-ddc367ed5c8936efe395cffeec279b04ffd7db78/server/bin/remote-cli:/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/root/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin:/root/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/bin:/root/bin
# environment
VSCODE_CLI_REQUIRE_TOKEN = ac7aade9-5889-496f-9953-b13c6a056e1c
# environment
XDG_RUNTIME_DIR = /run/user/0
# environment
ELECTRON_RUN_AS_NODE = 1
# environment
SELINUX_USE_CURRENT_RANGE = 
# default
.FEATURES := target-specific order-only second-expansion else-if shortest-stem undefine oneshell archives jobserver check-symlink
# automatic
%F = $(notdir $%)
# environment
SSL_CERT_FILE = /etc/pki/ca-trust/extracted/pem/tls-ca-bundle.pem
# environment
PWD = /root/q/t3_scu_hebing_V2_DKY
# environment
VSCODE_IPC_HOOK_CLI = /run/user/0/vscode-ipc-8f04fde7-9fd6-4919-bfc8-02daa6ead5bf.sock
# automatic
*D = $(patsubst %/,%,$(dir $*))
# environment
HOME = /root
# environment
LD_LIBRARY_PATH = :/root/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/aarch64-linux-gnu/lib64:/root/gcc-linaro-7.3.1-2018.05-x86_64_aarch64-linux-gnu/aarch64-linux-gnu/lib64
# environment
LOGNAME = root
# environment
APPLICATION_INSIGHTS_NO_DIAGNOSTIC_CHANNEL = true
# automatic
+F = $(notdir $+)
# environment
SELINUX_LEVEL_REQUESTED = 
# environment
VSCODE_HANDLES_UNCAUGHT_ERRORS = true
# automatic
^D = $(patsubst %/,%,$(dir $^))
# environment
HOSTNAME = localhost.localdomain
# environment
MAKELEVEL := 0
# default
MAKE = $(MAKE_COMMAND)
# default
MAKECMDGOALS := all
# environment
SHLVL = 5
# environment
VSCODE_ESM_ENTRYPOINT = vs/workbench/api/node/extensionHostProcess
# environment
XDG_SESSION_ID = 117
# environment
USER = root
# makefile
.DEFAULT_GOAL := 
# automatic
%D = $(patsubst %/,%,$(dir $%))
# default
MAKE_COMMAND := make
# default
.VARIABLES := 
# automatic
*F = $(notdir $*)
# makefile
MAKEFLAGS = Rrp
# environment
MFLAGS = -Rrp
# environment
SSH_CLIENT = ************* 53334 22
# environment
MAIL = /var/spool/mail/root
# automatic
+D = $(patsubst %/,%,$(dir $+))
# environment
VSCODE_L10N_BUNDLE_LOCATION = vscode-local:/c%3A/Users/<USER>/.vscode/extensions/ms-ceintl.vscode-language-pack-zh-hans-1.101.2025061109/translations/extensions/vscode.json-language-features.i18n.json
# environment
BROWSER = /root/.vscode-server/cli/servers/Stable-ddc367ed5c8936efe395cffeec279b04ffd7db78/server/bin/helpers/browser.sh
# environment
VSCODE_HANDLES_SIGPIPE = true
# default
MAKEFILES := 
# automatic
<F = $(notdir $<)
# environment
LC_ALL = C
# environment
SSL_CERT_DIR = /etc/pki/tls/certs
# automatic
^F = $(notdir $^)
# environment
SELINUX_ROLE_REQUESTED = 
# default
SUFFIXES := 
# environment
HISTSIZE = 1000
# default
.INCLUDE_DIRS = /usr/include /usr/local/include /usr/include
# default
.RECIPEPREFIX := 
# environment
LANG = C
# variable set hash-table stats:
# Load=69/1024=7%, Rehash=0, Collisions=6/86=7%

# Pattern-specific Variable Values

# No pattern-specific variable values.

# Directories


# No files, no impossibilities in 0 directories.

# Implicit Rules

# No implicit rules.

# Files

# Not a target:
all:
#  Command line target.
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

# Not a target:
.SUFFIXES:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

# Not a target:
/root/q/t3_scu_hebing_V2_DKY/src/MskpqAnalyzer/Makefile:
#  Implicit rule search has been done.
#  File does not exist.
#  File has not been updated.

# Not a target:
.DEFAULT:
#  Implicit rule search has not been done.
#  Modification time never checked.
#  File has not been updated.

# files hash-table stats:
# Load=4/1024=0%, Rehash=0, Collisions=0/14=0%
# VPATH Search Paths

# No `vpath' search paths.

# No general (`VPATH' variable) search path.

# # of strings in strcache: 0 / lookups = 9 / hits = 9
# # of strcache buffers: 1 (* 8176 B/buffer = 8176 B)
# strcache used: total = 0 (111) / max = 0 / min = 8176 / avg = 0
# strcache free: total = 0 (8065) / max = 0 / min = 8176 / avg = 0

# strcache hash-table stats:
# Load=6/8192=0%, Rehash=0, Collisions=0/9=0%
# Finished Make data base on Mon Aug 11 22:42:13 2025

 
