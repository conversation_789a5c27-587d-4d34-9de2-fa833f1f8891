{"desc": "pqAnalyzer", "body": [{"info": "acMeter_guid", "model": "TTU", "port": "0", "addr": "0", "desc": "TTUshujucaiji", "manuID": "123456", "manuName": "megsky", "ProType": "mqtt", "deviceType": "1234", "isReport": "0", "nodeID": "123", "productID": "11112222"}, {"info": "acMeter_data", "PhV_phsA": 0, "PhV_phsB": 0, "PhV_phsC": 0, "SeqV_c0": 0, "SeqV_c1": 0, "SeqV_c2": 0, "A_phsA": 0, "A_phsB": 0, "A_phsC": 0, "SeqA_c0": 0, "SeqA_c1": 0, "SeqA_c2": 0, "Hz": 0, "PhPF_phsA": 0, "PhPF_phsB": 0, "PhPF_phsC": 0, "TotPF": 0}, {"info": "param", "PowOff_Enable": 1, "PowerOff_V_Lim": 10, "PowerOff_Dly": 0, "PowOn_Enable": 0, "PowerOn_V_Lim": 154, "PowerOn_Dly": 0, "PTUA_Enable": 0, "PTUA_Dly": 5, "PTUA_Loss_Dly": 5, "PTUV_Enable": 0, "PTUV_Lim": 0.93, "PTUV_Dly": 5, "PTOV_Enable": 0, "PTOV_Lim": 1.07, "PTOV_Dly": 5, "Load_Enable": 0, "Load_Lim": 0.5, "Load_Dly": 5, "PTOC_Hvld_Enable": 1, "PTOC_Hvld_Lim": 0.8, "PTOC_Hvld_Dly": 2, "PTOC_Ovld_Enable": 0, "PTOC_Ovld_Lim": 1, "PTOC_Ovld_Dly": 2, "Seqc0OV_Enable": 0, "Seqc0OV_Lim": 120, "Seqc0OV_Dly": 5, "Seqc0OC_Enable": 0, "Seqc0OC_Lim": 6, "Seqc0OC_Dly": 5, "ResA_Enable": 0, "ResA_Lim": 0.5, "ResA_Dly": 0, "PTUPF_Enable": 0, "PTUPF_Lim": 0.75, "PTUPF_Dly": 5, "ImbA_Enable": 0, "Imb_db": 0.4, "ImbA_Lim": 0.15, "ImbNgA_phsN_Lim": 0.25, "ImbA_Dly": 5, "PhvImb_strVal": 0.15, "LoadImb_strVal": 0.4, "ARtg": 500, "ARtgSnd": 5, "zeroLineCTPrimaryRating": 500, "zeroLineCTSecondaryRating": 5, "RtgA": 0.3, "DeviceLoad": 200, "std_voltage": 220, "std_Current": 5, "std_Hz": 50, "Mqtt_Send_t": 2, "Stat_Interval_t": 1, "LoadRate_CalcCyc": 1, "ResLoad_CalcCyc": 1, "VRtg": 220, "VRtgSnd": 220, "inverse_offset_threshold_fs": 30, "A_Zero_Drift": 0.04, "PTUV_Open_V_Lim": 30, "PTUV_Open_Dly": 0, "DIP_Enable": 0, "DIP_strVal_Lim": 0.9, "DIP_Dly": 10, "SWL_Enable": 0, "SWL_strVal_Lim": 1.1, "SWL_Dly": 10, "INTR_Enable": 0, "INTR_strVal_Lim": 0.1, "INTR_Dly": 10, "PTUV_lim_setVal": 0.8, "PTOV_lim_setVal": 1.1, "PTUF_Enable": 0, "PTUF_Lim": 50.5, "PTUF_Dly": 3, "PTOF_Enable": 0, "PTOF_Lim": 49.5, "PTOF_Dly": 9, "ThdPhV_Op_V_Enable": 0, "ThdPhV_Op_V_Lim": 0.05, "ThdPhV_Op_V_Dly": 5, "ThdA_Op_A_Enable": 0, "ThdA_Op_A_Lim": 0.15, "ThdA_Op_A_Dly": 5}, {"info": "pqAnaly_model", "body": [{"name": "LoadRate", "type": "float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "LoadRate_phsA", "type": "float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "LoadRate_phsB", "type": "float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "LoadRate_phsC", "type": "float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "LoadMax", "type": "float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotResLoad", "type": "float", "unit": "kVA", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ResLoad_phsA", "type": "float", "unit": "kVA", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ResLoad_phsB", "type": "float", "unit": "kVA", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ResLoad_phsC", "type": "float", "unit": "kVA", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgV", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbA0", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbA2", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbV2", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbV0", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgLoad", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfs_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfs_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfs_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfs_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfs_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfs_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotHzOfs", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Day_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Day_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Day_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Day_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Day_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Day_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Day_phsA", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Day_phsB", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Day_phsC", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Day_phsA", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Day_phsB", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Day_phsC", "type": "Int", "unit": "Min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxDay_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxDay_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxDay_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinDay_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinDay_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinDay_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvDay_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvDay_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvDay_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVPassMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Mon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Mon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Rate_Mon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Mon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Mon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Rate_Mon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Mon_phsA", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Mon_phsB", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Tm_Mon_phsC", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Mon_phsA", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Mon_phsB", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Tm_Mon_phsC", "type": "Int", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxMon_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxMon_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMaxMon_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinMon_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinMon_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVMinMon_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvMon_phsA", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvMon_phsB", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVAvMon_phsC", "type": "Float", "unit": "V", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMaxMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsMinMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PhVOfsAvMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvDay_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvWeek_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvMon_phsA", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvDay_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvWeek_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvMon_phsB", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvDay_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvWeek_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMaxMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsMinMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "HzOfsAvMon_phsC", "type": "Float", "unit": "%", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TTU_Run_Tm", "type": "Float", "unit": "min", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgV_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgA_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgA_Alm_phsN", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgLoad_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "SeqOV_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Loss_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Loss_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Loss_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Loss_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Open_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Open_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Open_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_Open_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PowerOff_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PowerOn_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Open_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Open_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Open_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUC_Open_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "SeqOA_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Hvld_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Hvld_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Hvld_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Hvld_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUPF_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Ovld_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Ovld_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Ovld_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOC_Ovld_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Ovld_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "RevDir_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "RevDir_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "RevDir_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_Off_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_Off_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_Off_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_On_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_On_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PWR_On_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_lim_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_lim_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_lim_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_lim_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_lim_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_lim_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_lim_Op", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUV_lim_Op", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "DipStr_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "SwlStr_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "IntrStr_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOF_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOF_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOF_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOF_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUF_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUF_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUF_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUF_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUPF_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUPF_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUPF_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTUPF_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdPhV_Op_phsA_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdPhV_Op_phsB_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdPhV_Op_phsC_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdPhV_Op_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdA_Op_phsA_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdA_Op_phsB_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdA_Op_phsC_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ThdA_Op_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Seqc0OC_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ImbNgA_phsN_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotPF_Op_I_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotPF_Op_II_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotPF_Op_III_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "TotPF_Op_IV_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "PTOV_Open_Op", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "ResA_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Re_PTOC_Hvld_Alm", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Re_PTOC_Hvld_Op_phsA", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Re_PTOC_Hvld_Op_phsB", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}, {"name": "Re_PTOC_Hvld_Op_phsC", "type": "Boolean", "unit": "", "deadzone": "", "ratio": "1", "isReport": "1", "userdefine": ""}]}]}