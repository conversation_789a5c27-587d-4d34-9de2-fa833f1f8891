#include "task_thread.h"
#include "CJsonObject.h"
#include "mqtt_pub_inter.h"
extern char m_esn_sub[128];
extern uint8_t esn_flag;
extern gateway_connect_s m_lianwang;
uint8_t jiaoyan_flag = 0;
volatile MQTTClient_deliveryToken deliveredtoken_inter;
MQTTClient_connectOptions m_conn_opts_inter = MQTTClient_connectOptions_initializer;

CMqttClientInterThread::CMqttClientInterThread(void)
    : m_pMng(NULL)
{
}

CMqttClientInterThread::~CMqttClientInterThread()
{
}

void CMqttClientInterThread::SetPackageAcquireManager(CMqttClientInterManager *p)
{
    m_pMng = p;
}

void CMqttClientInterThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CMqttClientInterThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CMqttClientInterThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理者
//***************************************************************
CMqttClientInterManager &CMqttClientInterManager::CreateInstance()
{
    static CMqttClientInterManager Mng;
    return Mng;
}

void CMqttClientInterManager::agent_Delivered(void *context, MQTTClient_deliveryToken dt)
{
    // SGDEV_INFO(SYSLOG_LOG, SGDEV_MODULE, "Message with token value %d delivery confirmed.", dt);
    deliveredtoken_inter = dt;
}

void CMqttClientInterManager::agent_ConnLost(void *context, char *cause)
{
    if (cause != NULL)
    {
        IEC_LOG_RECORD(eErrType, "connLost(cause = %s).", cause);
        CMqttClientInterManager::CreateInstance().m_connectFlag = false;
    }
}

void CMqttClientInterManager::mqtt_pub_get_esn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        mskprintf("\nJson head fill failed---------.\n");
        return;
    }
    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    std::string mssm = oJson.ToString();
    C256String pub_topic;
    pub_topic.Format("MQTTIoT/OS-system/JSON/request/devInfo");
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    std::string aaa = pub_topic.ToString();
    mskprintf("mssm:%s.\n", mssm.c_str());
    mskprintf("pub_topic:%s.\n", aaa.c_str());
}

void CMqttClientInterManager::mqtt_pub_set_route()
{
    //     mqtt_header_s jsheader = { 0 };
    //     int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    //     if (ret != MQTT_OK)
    //     {
    //         mskprintf("\nJson head fill failed---------.\n");
    //         return;
    //     }
    //     neb::CJsonObject oJson;
    //     oJson.Add("token",123);
    //     oJson.Add("timestamp",jsheader.timestamp);
    //     neb::CJsonObject body;
    //     body.Add("destIp",m_lianwang.destIp);
    //     body.Add("netMask",m_lianwang.netMask);
    //     body.Add("gateway",m_lianwang.gateway);
    //     body.Add("interface",m_lianwang.interface);
    //     oJson.Add("body",body);
    //     std::string mssm = oJson.ToString();
    //     C256String pub_topic;
    //     pub_topic.Format("MQTTIoT/OS-system/JSON/request/addRoute");
    //     if (mssm.size() > 0) {
    //         mqtt_data_info_s item = { 0 };
    //         memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char*)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
    //         memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
    //         item.msg_send_lenth = mssm.size();
    //         item.retained = 1;
    //         CMqttClientInterManager::CreateInstance().Push_SendItem(item);

    //     }
    //     std::string aaa = pub_topic.ToString();
    //    mskprintf("mssm:%s.\n",mssm.c_str());
    //     mskprintf("pub_topic:%s.\n",aaa.c_str());
}
// #define ESN_PATH  "/data/app/"

bool CMqttClientInterManager::Init(void)
{
    bool bRet(false);
    mqtt_data_info_s Sitem = {0};
    mqtt_data_info_s Ritem = {0};
    CIIPara myParam;
    std::string sTempValue;

    // 初始主题订阅
    m_topics.clear();
    // m_topics.push_back("+/MQTTIot/JSON/report/notification/terminalStatus/+");              // 业务app上线订阅
    // m_topics.push_back("+/MQTTIot/JSON/report/notification/terminalData/+");                // 端设备主动上报数据topic订阅
    // m_topics.push_back("+/MQTTIot/JSON/action/response/ terminalCmd/+");                    //业务命令发布响应topic订阅
    m_topics.push_back("OS-system/MQTTIoT/JSON/response/devInfo");
    // m_topics.push_back("OS-system/MQTTIoT/JSON/response/register");
    // m_topics.push_back("OS-system/MQTTIoT/JSON/request/keepAlive")    ;
    // m_topics.push_back("dataCenter/MQTTIoT/JSON/get/response/realtime") ;
    // m_topics.push_back("acMeter/OS-system/JSON/response/keepAlive");
    // // m_topics.push_back("/v1/devices/+/command") ;
    // // m_topics.push_back("OS-system/MQTTIoT/JSON/response/setTime") ;
    // m_topics.push_back("OS-system/MQTTIoT/JSON/response/addRoute");
    // m_topics.push_back("OS-system/MQTTIoT/JSON/response/setSshMode");
    // m_topics.push_back("MQTTTrData/MQTTIot/JSON/action/response/terminalCmd/+") ;
    m_topics.push_back("/v1/devices/+/topo/add");
    m_topics.push_back("/v1/devices/+/topo/update");
    m_topics.push_back("/v1/devices/+/topo/query");
    m_topics.push_back("/v1/devices/+/commandResponse");
    m_topics.push_back("/v1/devices/+/datas");
    char str[256] = {0};
    SMTimeInfo local_time = ii_get_current_mtime();
    sprintf(str, "megskyiot%d%dstd", local_time.nSecond, local_time.nMSecond);
    // m_clientid = "megskyiec";
    m_clientid = str;
    m_ip = "**********";
    m_port = "1883";
    char server_uri[256] = {0};
    sprintf(server_uri, "tcp://%s:%s", m_ip.c_str(), m_port.c_str());

    IEC_LOG_RECORD(eRunType, "mqtt connect url =(%s).", server_uri);

    if (agent_mqtt_init((const char *)server_uri, m_clientid.c_str()) != true)
    {
        IEC_LOG_RECORD(eErrType, "agent mqtt init failed.");
        bRet = false;
    }

    if (agent_mqtt_connect())
    { // 初始化成功后尝试一次连接
        if (agent_creatSubTopic())
        {
            m_connectFlag = true;
        }
    }
    if ((m_lianwang.destIp.empty() == true) && (m_lianwang.interface.empty() == true))
    {

        mqtt_pub_set_route();
    }
    else
    {
        IEC_LOG_RECORD(eErrType, "destIp or interface is NULL");
    }
    mqtt_pub_get_esn();
    while (!esn_flag)
    {
        ii_sleep(1000 * 5);
        if (Get_RecvItem(Ritem))
        {
            CTaskManager::CreateInstance().UnpackData(Ritem); // 处理收到的数据
            if(esn_flag)
            {
                break;
            }
        }
        mqtt_pub_get_esn();
        if (Get_SendItem(Sitem))
        {
            agent_MqttMsgPub(Sitem.msg_send, Sitem.pubtopic, Sitem.retained); // 取到发送队列数据后发送
        }
    }

    // app_enroll_OS();
    // app_enroll_OS();
    bRet = m_Thread.start();
    return bRet;
}
bool CMqttClientInterManager::agent_mqtt_init(const char *server_uri, const char *client_id)
{
    CIIString iisTemp;

    int mqtt_ret;
    if (server_uri == NULL || client_id == NULL)
    {
        IEC_LOG_RECORD(eErrType, "MQTT Init param failed.");
        return false;
    }

    m_conn_opts_inter.keepAliveInterval = SG_KEEP_ALIVE_INTERVAL;
    m_conn_opts_inter.cleansession = 1;
    // options.setAutomaticReconnect(true);
    if (m_user.size() != 0 && m_password.size() != 0)
    {
        m_conn_opts_inter.username = m_user.c_str();
        m_conn_opts_inter.password = m_password.c_str();
    }

    mqtt_ret = MQTTClient_create(&m_client, server_uri, client_id, MQTTCLIENT_PERSISTENCE_NONE, NULL);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "MQTTClient create failed(server_uri = %s,clientid = %s,ret = %d).", server_uri, client_id, mqtt_ret);
        return false;
    }

    IEC_LOG_RECORD(eRunType, "MQTTClient create success(server_uri = %s,clientid = %s).", server_uri, client_id);

    mqtt_ret = MQTTClient_setCallbacks(m_client, NULL, agent_ConnLost, agent_MqttMsgArrvd, agent_Delivered);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        agent_mqtt_destroy();
        return false;
    }

    IEC_LOG_RECORD(eRunType, "agent mqtt init success(server_uri = %s,clientid = %s).", server_uri, client_id);
    return true;
}
void CMqttClientInterManager::Exit(void)
{
    m_Thread.close();
    m_Thread.wait_for_end();
    agent_destrySubTopic();
    agent_mqtt_disconnect();
    agent_mqtt_destroy();
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
}

void CMqttClientInterManager::thread_prev(void)
{
}

void CMqttClientInterManager::thread_func(void)
{
    OnRun();
}

void CMqttClientInterManager::thread_exit(void)
{
}
void CMqttClientInterManager::OnRun(void)
{
    mqtt_data_info_s Sitem = {0};
    mqtt_data_info_s Ritem = {0};
    if (Get_SendItem(Sitem))
    {
        agent_MqttMsgPub(Sitem.msg_send, Sitem.pubtopic, Sitem.retained); // 取到发送队列数据后发送
    }

    if (Get_RecvItem(Ritem))
    {
        CTaskManager::CreateInstance().UnpackData(Ritem); // 处理收到的数据
    }

    ii_sleep(10);
}

void CMqttClientInterManager::Start(void)
{
}

void CMqttClientInterManager::Stop(void)
{
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
}

bool CMqttClientInterManager::getMqttConnect(void)
{
    if (!MQTTClient_isConnected(m_client))
    {
        m_connectFlag = false;
    }
    else
    {
        m_connectFlag = true;
    }
    return m_connectFlag;
}

bool CMqttClientInterManager::agent_mqtt_connect(void)
{
    int mqtt_ret = VOS_OK;
    m_connectFlag = false;
    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt client id handle invalid).");
        return false;
    }

    IEC_LOG_RECORD(eRunType, "mqtt connecting ....");
    mqtt_ret = MQTTClient_connect(m_client, &m_conn_opts_inter);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "mqtt connect failed(ret = %d)).", mqtt_ret);
        m_connectFlag = false;
        return false;
    }

    IEC_LOG_RECORD(eRunType, "mqtt connect success.");
    m_connectFlag = true;
    return true;
}

void CMqttClientInterManager::agent_mqtt_disconnect(void)
{
    int mqtt_ret;

    if (m_client != NULL)
    {
        mqtt_ret = MQTTClient_disconnect(m_client, MQTT_DISCONNECT_TIMEOUT);
        m_connectFlag = false;
        if (mqtt_ret != MQTTCLIENT_SUCCESS)
        {
            IEC_LOG_RECORD(eRunType, "mqtt disconnect error.");
        }
    }
}

void CMqttClientInterManager::agent_mqtt_destroy(void)
{
    if (m_client != NULL)
    {
        MQTTClient_destroy(&m_client);
        IEC_LOG_RECORD(eRunType, "mqtt destroy.");
        m_client = NULL;
    }
}

bool CMqttClientInterManager::agent_MqttMsgPub(char *msg_send, char *pub_topic, int retained)
{
    int mqtt_ret = 0;
    int msglen = 0;
    CIIString iisTemp;
    MQTTClient_deliveryToken token;
    MQTTClient_message pubmsg = MQTTClient_message_initializer;
    if (msg_send == NULL || pub_topic == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt publish param msg_send or pubtopicinvalid.");
        return false;
    }

    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt client id handle invalid.");
        return false;
    }

    if (!MQTTClient_isConnected(m_client))
    {
        IEC_LOG_RECORD(eErrType, "mqtt client disconnected.");
        m_connectFlag = false;
    }
    else
    {
        m_connectFlag = true;
    }

    if (m_connectFlag == false)
    {
        return true;
    }
    // msglen = (int)strlen(msg_send) + 1; //2023 0805 17点26分
    msglen = (int)strlen(msg_send);
    pubmsg.payload = msg_send;
    pubmsg.payloadlen = msglen;
    pubmsg.qos = QOS;
    // if (strcmp(pub_topic, "") == 0)
    pubmsg.retained = retained;

    mqtt_ret = MQTTClient_publishMessage(m_client, pub_topic, &pubmsg, &token);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        iisTemp.Format("mqtt publish failed.(topic=%s, ret=%d)", pub_topic, mqtt_ret);
        IEC_LOG_RECORD(eErrType, iisTemp.GetBuf());
        return false;
    }

    mqtt_ret = MQTTClient_waitForCompletion(m_client, token, TIMEOUT);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        iisTemp.Format("MQTTClient waitForCompletion ret=%d).", mqtt_ret);
        IEC_LOG_RECORD(eErrType, iisTemp.GetBuf());
        return false;
    }
    return true;
}

int CMqttClientInterManager::agent_MqttMsgArrvd(void *context, char *topicName, int topicLen, MQTTClient_message *message)
{
    CIIAutoMutex lock(&CMqttClientInterManager::CreateInstance().m_cs);
    char *content_str = NULL;
    if (message == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt msg is null.");
        MQTTClient_free(topicName);
        return 1;
    }

    if (message->payloadlen > 0)
    {
        content_str = (char *)malloc((size_t)(message->payloadlen + 1));
        if (content_str)
        {
            memcpy(content_str, message->payload, (size_t)(message->payloadlen));
            content_str[message->payloadlen] = 0;
            mqtt_data_info_s item = {0};
            sprintf(item.pubtopic, "%s", topicName);
            memcpy_safe(item.msg_send, sizeof(item.msg_send), content_str, message->payloadlen, message->payloadlen);
            item.msg_send_lenth = message->payloadlen;
            CMqttClientInterManager::CreateInstance().Push_RecvItem(item);
            (void)memset(content_str, 0, (size_t)(message->payloadlen + 1));
            (void)memset(message->payload, 0, (size_t)(message->payloadlen));
            free(content_str);
            content_str = NULL;
        }
        else
        {
            IEC_LOG_RECORD(eErrType, "mqtt msg malloc failed.");
        }
    }
    MQTTClient_freeMessage(&message);
    MQTTClient_free(topicName);
    return 1;
}

bool CMqttClientInterManager::agent_mqtt_msg_subscribe(char *topic, int qos)
{
    int mqtt_ret;
    if (topic == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe topic invalid.");
        return false;
    }

    if (strlen(topic) > 256)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe topic over max 256.");
        return false;
    }

    if (m_client == NULL)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe client id handle invalid).");
        return false;
    }

    mqtt_ret = MQTTClient_subscribe(m_client, topic, qos);
    if (mqtt_ret != MQTTCLIENT_SUCCESS)
    {
        IEC_LOG_RECORD(eErrType, "mqtt subscribe failed(ret = %d,pub_topic = %s)).", mqtt_ret, topic);
        return false;
    }
    IEC_LOG_RECORD(eErrType, "mqtt subscribe succeed(pub_topic = %s)).", topic);
    return true;
}

bool CMqttClientInterManager::agent_creatSubTopic(void)
{
    bool ret = true;
    std::list<std::string>::iterator it = m_topics.begin();
    for (; it != m_topics.end(); ++it)
    {
        if (!agent_mqtt_msg_subscribe((char *)(*it).c_str(), QOS))
        {
            ret = false;
        }
    }

    return ret;
}
void CMqttClientInterManager::agent_destrySubTopic()
{
    std::list<std::string>::iterator it = m_topics.begin();
    for (; it != m_topics.end(); ++it)
    {
        MQTTClient_unsubscribe(m_client, (*it).c_str());
    }
    // MQTTClient_unsubscribe(m_client, m_topic_other);
}

Juint32 CMqttClientInterManager::MqttGetRandomUuid(char *pucUuid, Juint32 ulLen)
{
    Juint32 ulIndex;
    Juint32 randomNum;
    char *pucUuidItem = pucUuid;

    if (NULL == pucUuid)
    {
        MG_LOG_E("VOS_GetRandomUuid in put invalid.\n");
        return VOS_ERR;
    }

    srand(time(0));
    for (ulIndex = 0; ulIndex < 16; ulIndex++)
    {
        randomNum = (rand() % 255);
        sprintf(pucUuidItem, "%02x", randomNum);
        pucUuidItem += 2;

        switch (ulIndex)
        {
        /* 3 5 7 9 都会执行添加- */
        case 3:
        case 5:
        case 7:
        case 9:
            *pucUuidItem++ = '-';
            break;
        }
    }
    *pucUuidItem = '\0';

    return VOS_OK;
}
/*****************************************************************************
 函 数 名  : MqttGenerateToken
 功能描述  : 生成token
 输入参数  :
 输出参数  :
 返 回 值  :
 调用函数  :
 被调函数  :
*****************************************************************************/
int CMqttClientInterManager::MqttGenerateToken(char *token, size_t buf_len)
{

    if (MQTT_OK != MqttGetRandomUuid(token, buf_len))
    {
        MG_LOG_E("generate token failed\n");
        return MQTT_ERR;
    }
    // MG_LOG("generated token [%s]\n", token);
    return MQTT_OK;
}
int CMqttClientInterManager::MqttHeaderFill(mqtt_header_s *header, const char *request_token)
{
    // time_t now_time;
    int ret = MQTT_ERR;

    if (request_token) // 如果请求token非空，说明现在填充的是响应中的token，响应token与请求token相同
    {
        (void)memcpy(header->token, request_token, sizeof(header->token));
    }

    else
    {
        // MG_LOG("\nheader token len is %d.\n", sizeof(header->token));
        ret = MqttGenerateToken(header->token, sizeof(header->token));
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nFill json header failed for generate token.\n");
            return MQTT_ERR;
        }
    }

    // 使用UTC时间
    ret = MqttTimeStr(header->timestamp, sizeof(header->timestamp));
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nFill json header failed for generate time str.\n");
        return MQTT_ERR;
    }
    return MQTT_OK;
}

/*****************************************************************************
 函 数 名  : MqttTimeStr
 功能描述  : 生成时间戳
 输入参数  :
 输出参数  :
 返 回 值  :
 调用函数  :
 被调函数  :
*****************************************************************************/
int CMqttClientInterManager::MqttTimeStr(char *time_buff, size_t buff_len)
{
    int ret;
    struct timeval tv;
    struct timezone tz;
    struct tm *t;

    gettimeofday(&tv, &tz);
    t = localtime(&tv.tv_sec);
    // ret = snprintf(time_buff,buff_len,"%04d-%02d-%02dT%02d:%02d:%02d.%03d+0800",
    // 1900+t->tm_year, 1+t->tm_mon, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec, (int)tv.tv_usec/1000);

    ret = snprintf(time_buff, buff_len, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                   1900 + t->tm_year, 1 + t->tm_mon, t->tm_mday, t->tm_hour, t->tm_min, t->tm_sec);

    return ret > 0 ? MQTT_OK : MQTT_ERR;
}

void CMqttClientInterManager::Push_RecvItem(mqtt_data_info_s item)
{
    m_mqttRecvQueue.AddTail(item);
}

bool CMqttClientInterManager::Get_RecvItem(mqtt_data_info_s &item)
{
    return m_mqttRecvQueue.GetHead(item);
}

void CMqttClientInterManager::Push_SendItem(mqtt_data_info_s item)
{
    m_mqttSendQueue.AddTail(item);
}

bool CMqttClientInterManager::Get_SendItem(mqtt_data_info_s &item)
{
    return m_mqttSendQueue.GetHead(item);
}

CMqttClientInterManager::CMqttClientInterManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_mqttSendQueue.Clear();
    m_mqttRecvQueue.Clear();
    m_topics.clear();
    m_T.SetParam(200 * 1000); // 默认200秒
    m_client = NULL;
}

CMqttClientInterManager::~CMqttClientInterManager()
{
}
