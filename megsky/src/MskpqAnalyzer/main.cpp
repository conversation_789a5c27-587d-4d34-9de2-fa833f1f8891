#include <execinfo.h>
#include <cxxabi.h>
#include <signal.h>
#include "public_struct.h"
#include "mqtt_pub_inter.h"
#include "task_thread.h"
#include "param_json.h"
#include <openssl/md5.h>
void sgdevagent_ignore_sig(int signum);
void sgdevagent_sighandler(int signo);
void sg_handle_signal(void);

void sgdevagent_ignore_sig(int signum)
{
    struct sigaction sa;
    sigemptyset(&sa.sa_mask);
    (void)memset(&sa, 0, sizeof(sa));
    sa.sa_handler = SIG_IGN;
    (void)sigaction(signum, &sa, 0);
}


#define BACK_TRACES_ARRAY_SIZE          20
void sgdevagent_sighandler(int signo)
{
    IEC_LOG_RECORD(eErrType, "APP receive sigo:%d", signo);
    switch (signo) {
    case SIGQUIT:
    case SIGILL:
    case SIGBUS:
    case SIGFPE:
    case SIGSEGV:
        // SSP_Backtrace("SGDEVAGENT");
        break;
    default:
        break;
    }

    int i;
    int depth = 0;
    char** symbols = NULL;
    char* stack[BACK_TRACES_ARRAY_SIZE] = { 0 };

    IEC_LOG_RECORD(eErrType, "Signal caught:%u, dumping backtrace...\n", signo);
    depth = backtrace((void**)(stack), sizeof(stack) / sizeof(stack[0]));
    if (depth)
    {
        symbols = backtrace_symbols((void**)(stack), depth);
        if (symbols)
        {
            for (i = 0; i < depth; i++)
            {
                IEC_LOG_RECORD(eErrType, "===[%u]:%s.", (i + 1), symbols[i]);
            }
        }
        free(symbols);
    }

    //sleep(5);
    //re-throw 
    raise(signo);

    //(void)kill(getpid(), signo);
    return;
}

void sg_handle_signal(void)
{
    struct sigaction action;

    (void)sigemptyset(&action.sa_mask);
    action.sa_flags = (int)(SA_NODEFER | SA_ONESHOT | SA_SIGINFO);
    action.sa_handler = sgdevagent_sighandler;

    (void)sigaction(SIGINT, &action, NULL);          // 2 中断进程
    (void)sigaction(SIGQUIT, &action, NULL);         // 3 终止进程
    (void)sigaction(SIGILL, &action, NULL);          // 4 非法指令
    (void)sigaction(SIGBUS, &action, NULL);          // 7 总线错误
    (void)sigaction(SIGFPE, &action, NULL);          // 8 浮点异常
    (void)sigaction(SIGSEGV, &action, NULL);         // 11 段非法错误
    (void)sigaction(SIGTERM, &action, NULL);         // 15 软件终止
    (void)sigaction(SIGXCPU, &action, NULL);         // 24 CPU资源限制
    (void)sigaction(SIGXFSZ, &action, NULL);         // 25 文件大小限制
    (void)sigaction(SIGSYS, &action, NULL);          // 31 系统调用异常
}

int main(int argv, char *args[])
{
    ii_sleep(1000 * 2);
    
    folder_mkdirs(IEC_EXCFG_PATH);
    folder_mkdirs(IEC_EXCFG_PATH "logFile");
    folder_mkdirs(IEC_EXCFG_PATH "configFile");
    folder_mkdirs(IEC_EXCFG_PATH "commFile");

    int file_ret = copyFile(DEVICE_INFO_FILEPATH,DEVICE_INFO_FILEPATH_ABS);
    cout<<"file_ret: "<<file_ret<<endl;
    
    sgdevagent_ignore_sig(SIGPIPE);
    sgdevagent_ignore_sig(SIGRTMIN + 4);
    sg_handle_signal();    // 信号处理

	int nRet = 0;
    bool bPrintFlag = false;
    printf_enable(false);
    if (argv > 1)
    {
        if (strncmp(args[1], "-p", strlen("-p")) == 0)
        {
            printf_enable(true);
            mskprintf("printf_enable true! \n");
            bPrintFlag = true;
        }
    }

    CLogFile::Instance().SetFileName(IEC_LOG_PATH, 1024 * 1024 * LOG_FILE_SIZE);//软件内部日志 单位字节 
    CLogFile::Instance().StartThread();
    IEC_LOG_RECORD(eRunType, "Software Version (MSKYF 01.003, (%s %s)).", __DATE__,__TIME__);

    ii_sleep(1000 * 5);

    if (!CParamManager::CreateInstance().Init()) 
    {
        IEC_LOG_RECORD(eErrType, "Param init failed.");
        return VOS_ERR;
    }

    IEC_LOG_RECORD(eRunType, "Param init success."); 

    if (!CMqttClientInterManager::CreateInstance().Init())   // MQTT初始化
    {
        IEC_LOG_RECORD(eErrType, "mqtt init failed.");
        CLogFile::Instance().StopThread();
        return VOS_ERR;
    }

    IEC_LOG_RECORD(eRunType, "mqtt init success.");

    if (!CTaskManager::CreateInstance().Init()) 
    {
        IEC_LOG_RECORD(eErrType, "task init failed.");
        CMqttClientInterManager::CreateInstance().Exit();
        CLogFile::Instance().StopThread();
        return VOS_ERR;
    }

    IEC_LOG_RECORD(eRunType, "task init success.");

    for (;;) {
        if (!CMqttClientInterManager::CreateInstance().getMqttConnect()) {
            if (CMqttClientInterManager::CreateInstance().agent_mqtt_connect() != true) {
                ii_sleep(1000 * 30);
                mskprintf("mqtt connect failed.\n");
                continue;
            }
            ii_sleep(1000);
            //CMqttClientInterManager::CreateInstance().agent_destrySubTopic();
            if (CMqttClientInterManager::CreateInstance().agent_creatSubTopic() != true) {
                IEC_LOG_RECORD(eRunType, "mqtt connect subscribe failed.");
                return VOS_ERR;
            }
        }
        char filePath[PATH_MAX] = { 0 };
        if (bPrintFlag || realpath(IEC_PRINT_FILEPATH, filePath) != NULL) {
            printf_enable(true);
        } else {
            printf_enable(false);
        }

        ii_sleep(1000 * 10);
    }

    CTaskManager::CreateInstance().Exit();
	CMqttClientInterManager::CreateInstance().Exit();
    CLogFile::Instance().StopThread();
	return nRet;
}

