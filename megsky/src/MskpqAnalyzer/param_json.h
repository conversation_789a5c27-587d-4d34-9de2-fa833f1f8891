

/*=====================================================================
 * 文件：param_json.h
 *
 * 描述：读取的模型文件
 *
 * 作者：田振超			2021年9月27日13:45:06
 * 
 * 修改记录：
 =====================================================================*/


#ifndef PARAM_JSON_H
#define PARAM_JSON_H

#include "public_struct.h"
#include "pub_string.h"
#include "pub_thread.h"
#include "pub_logfile.h"
#include "queue_model.h"
#include "CJsonObject.h"



class CFileObj 
{
public:
    CFileObj();
    virtual~CFileObj();

public:
    std::string             m_model;
    std::string             m_file;
    std::string             m_Topic;
    std::list<guid_body_s>  m_devs;
};

//====================================================================================
//线程
//=====================================================================================
class CParamManager;
class CParamThread : public CSL_thread
{
public:
    CParamThread();
    virtual~CParamThread();
    void SetPackageAcquireManager(CParamManager * p);

    virtual void run(void);
    virtual std::string ThreadName(void) const;
    virtual void RecordLog(const std::string & sMsg);

private:
    CParamManager * m_pMng;
};

//=====================================================================================
//管理者
//=====================================================================================
class CParamManager
{
public:
    static CParamManager & CreateInstance();

    bool Init(void);
    void Exit(void);

    void thread_prev(void);
    void thread_func(void);
    void thread_exit(void);

    void OnRun(void);
    void Start(void);
    void Stop(void);

    std::string CreatMd5(std::string str);

    int SetConstParam(std::string paramname,float paramval); //保存定值设置参数 

    int SetAdcData(std::string paramname,float paramval); //保存交采数据

    bool Unpack_pdAnalyzerData();   //计算电能质量分析

    bool Unpack_ocAnalyzerData();   //计算可开放容量分析

    bool Unpack_pdAnalyzerImb();   //计算不平衡度

    float calculate_imbalance(float a, float b, float c);
    float calculate_imbalance_v(float a, float b, float c);

    int voltage_limit_analysis(float  interval_t,std::string strtime);

    int voltage_yx_analysis();              //遥信状态判断，1秒1次
    int voltage_yx_analysis_ADC();          //遥信状态判断，交采测试项
    int voltage_yx_analysis_PTOV_Alm();         //遥信状态判断-过压告警
    int voltage_yx_analysis_SeqOV_Alm();        //遥信状态判断-零序过压告警
    int voltage_yx_analysis_PTUV_Alm();         //遥信状态判断-欠压告警
    int voltage_yx_analysis_PTUV_Loss_Alm();    //遥信状态判断-失压告警
    int voltage_yx_analysis_PowerOn_Alm();      //遥信状态判断-有压告警
    int voltage_yx_analysis_PTUV_Open_Alm();    //遥信状态判断-电源断相
    int voltage_yx_analysis_PhsSeqV_Alm();      //遥信状态判断-电压逆相序

    int current_yx_analysis_PTUC_Alm();         //遥信状态判断-电源矢流
    int current_yx_analysis_PTUC_Open_Alm();    //遥信状态判断-电源断流
    int current_yx_analysis_PTOA_Alm();         //遥信状态判断-电流过流
    int current_yx_analysis_SeqOA_Alm();        //遥信状态判断-零序过流
    int current_yx_analysis_Ovld_Alm();         //遥信状态判断-负荷越线
    int current_yx_analysis_PTOC_Hvld_Alm();    //遥信状态判断-配变重载
    int current_yx_analysis_PTOC_Ovld_Alm();    //遥信状态判断-配变过载
    int current_yx_analysis_Res_Alm();          //剩余电流超限
    int PT_yx_analysis_PTUPF_Alm();             //功率因数越限
    int current_yx_analysis_DIP_strVal_Alm();   //电压暂降
    int current_yx_analysis_ThdPhV_Op_Alm();    //电压总谐波越限告警
    int current_yx_analysis_ThdA_Op_Alm();      //电流总谐波越限告警

    int current_yx_analysis_TotHzOfs_Alm();     //频率越限 
    int current_yx_analysis_PTOV_Open_Op();     //10KV缺相研判
    // int current_yx_analysis_                  //低压二次回路断线


    void judge_yx_status();//判断遥信是否到达上报条件。
    int judge_m_TCalOvertime();//遥信不平衡度   

    float findMiddle(float a, float b, float c); //取中间值
    void initYXData();
    void initADCData();
    void initDayData();
    void initWeekData();
    void initMonData();
    void initocData();
    void initoc_Load_Data();
    
    CParamManager();
    ~CParamManager();

    CParamManager& operator = (const CParamManager&);
    CParamManager(const CParamManager&);
private:

    //std::string UnpackToken(neb::CJsonObject obj);                           // 获取Token值
    bool ReadJsonFile();

    bool ReadJsonFile_preData();
    bool WriteJsonFile_preData();
    bool ReadModelFile();
    // 发送数据 

    std::string m_databuffer;
    neb::CJsonObject m_obj_body_dev;
    neb::CJsonObject m_obj_body_adc;
    neb::CJsonObject m_obj_body_para;
    neb::CJsonObject m_obj_body_model; 
    
private:
    // 截取字符串 
       
    std::string GetFirsAppName(std::string topic);
public:
    std::list<CFileObj>                     m_FileObjs;
    Juint32                                 m_sendPeriod;
    Juint32                                 m_sendNum;
    Juint32                                 m_yxcycle;
    
    //交采信息
    std::string m_devModel;
    std::string m_devPort;
    std::string m_devAddr;
    std::string m_devDesc;
    std::string m_devmanuID;
    std::string m_devmanuName;
    std::string m_devProType;
    std::string m_devdeviceType;
    std::string m_devisReport;
    std::string m_devnodeID;
    std::string m_devproductID;
    std::string m_devGuid;
    std::string m_devDev;
    std::string m_appName;    
    int m_appVersion;
    std::string m_appRealseDate;

    int m_OS_statusCode;
    std::string m_OS_statusDesc;

    //MCCB信息
    std::string m_MCCBdevModel;
    std::string m_MCCBdevPort;
    std::string m_MCCBdevAddr;
    std::string m_MCCBdevDesc;
    std::string m_MCCBdevmanuID;
    std::string m_MCCBdevmanuName;
    std::string m_MCCBdevProType;
    std::string m_MCCBdevdeviceType;
    std::string m_MCCBdevisReport;
    std::string m_MCCBdevnodeID;
    std::string m_MCCBdevproductID;
    std::string m_MCCBdevGuid;
    std::string m_MCCBdevDev;
    std::string m_MCCBappName;   

    //模型注册信息
    std::vector<model_item_s> m_vmodel;
    
    //电能质量信息时间统计  
    voltage_det_stat_s m_voltage_time;

    int  m_Mqtt_Send_t;   //MQTT发送频率
    int  m_Stat_Interval_t;   //电能质量分析频率
    std::map<std::string,float> m_ADC_value;              //交采数据
	std::map<std::string,float> m_ADC_old_value;//老版数
    std::map<std::string,float> m_pdAnalyzer_value;       //电能质量分析值 -动态属性
    std::map<std::string,int> m_pdAnalyzer_value_YX;      //电能质量分析值 -消息域
    std::map<std::string,float> m_Param_value;            //定值数据

    std::string m_PhVMaxDay_phsA_TIME;      //最大A相电压时间
    std::string m_PhVMaxDay_phsB_TIME;      //最大B相电压时间
    std::string m_PhVMaxDay_phsC_TIME;      //最大C相电压时间

    std::string m_PhVMinDay_phsA_TIME;      //最小A相电压时间
    std::string m_PhVMinDay_phsB_TIME;      //最小B相电压时间
    std::string m_PhVMinDay_phsC_TIME;      //最小C相电压时间

    std::string m_PhVMaxMon_phsA_TIME;      //最大A相电压时间
    std::string m_PhVMaxMon_phsB_TIME;      //最大B相电压时间
    std::string m_PhVMaxMon_phsC_TIME;      //最大C相电压时间

    std::string m_PhVMinMon_phsA_TIME;      //最小A相电压时间
    std::string m_PhVMinMon_phsB_TIME;      //最小B相电压时间
    std::string m_PhVMinMon_phsC_TIME;      //最小C相电压时间
    

    float m_Tol_PhV_Day_phsA;//计算平均电压
    int m_Count_PhV_Day_phsA;
    float m_Tol_PhV_Day_phsB;
    int m_Count_PhV_Day_phsB;
    float m_Tol_PhV_Day_phsC;
    int m_Count_PhV_Day_phsC;

    float m_Tol_PhV_Mon_phsA;//计算平均电压
    int m_Count_PhV_Mon_phsA;
    float m_Tol_PhV_Mon_phsB;
    int m_Count_PhV_Mon_phsB;
    float m_Tol_PhV_Mon_phsC;
    int m_Count_PhV_Mon_phsC;

    
    float m_Tol_PhVOfs_Day_phsA;//计算平均电压偏差
    int m_Count_PhVOfs_Day_phsA;
    float m_Tol_PhVOfs_Day_phsB;
    int m_Count_PhVOfs_Day_phsB;
    float m_Tol_PhVOfs_Day_phsC;
    int m_Count_PhVOfs_Day_phsC;

    float m_Tol_PhVOfs_Week_phsA;//计算平均电压偏差
    int m_Count_PhVOfs_Week_phsA;
    float m_Tol_PhVOfs_Week_phsB;
    int m_Count_PhVOfs_Week_phsB;
    float m_Tol_PhVOfs_Week_phsC;
    int m_Count_PhVOfs_Week_phsC;

    float m_Tol_PhVOfs_Mon_phsA;//计算平均电压偏差
    int m_Count_PhVOfs_Mon_phsA;
    float m_Tol_PhVOfs_Mon_phsB;
    int m_Count_PhVOfs_Mon_phsB;
    float m_Tol_PhVOfs_Mon_phsC;
    int m_Count_PhVOfs_Mon_phsC;

    float m_Tol_HzOfs_Day_phsA;//计算平均频率偏差
    int m_Count_HzOfs_Day_phsA;
    float m_Tol_HzOfs_Day_phsB;
    int m_Count_HzOfs_Day_phsB;
    float m_Tol_HzOfs_Day_phsC;
    int m_Count_HzOfs_Day_phsC;

    float m_Tol_HzOfs_Week_phsA;//计算平均频率偏差
    int m_Count_HzOfs_Week_phsA;
    float m_Tol_HzOfs_Week_phsB;
    int m_Count_HzOfs_Week_phsB;
    float m_Tol_HzOfs_Week_phsC;
    int m_Count_HzOfs_Week_phsC;

    float m_Tol_HzOfs_Mon_phsA;//计算平均频率偏差
    int m_Count_HzOfs_Mon_phsA;
    float m_Tol_HzOfs_Mon_phsB;
    int m_Count_HzOfs_Mon_phsB;
    float m_Tol_HzOfs_Mon_phsC;
    int m_Count_HzOfs_Mon_phsC;


    float m_pre_LoadRate;   //上一次配变负载率
    float m_pre_LoadRate_phsA;//上一次配变负载率A
    float m_pre_LoadRate_phsB;//上一次配变负载率B
    float m_pre_LoadRate_phsC;//上一次配变负载率C

    float m_LoadRate_sum;   //配变负载率总和
    float m_LoadRate_phsA_sum;   //配变负载率总和a
    float m_LoadRate_phsB_sum;   //配变负载率总和B
    float m_LoadRate_phsC_sum;   //配变负载率总和C

    int m_LoadRate_num;     //配变负载率计算数量    
    int m_LoadRate_phsA_num;     //配变负载率计算数量a    
    int m_LoadRate_phsB_num;     //配变负载率计算数量B    
    int m_LoadRate_phsC_num;     //配变负载率计算数量C

    float m_LoadRate;       //配变负载率
    float m_LoadRate_phsA;       //配变负载率A 
    float m_LoadRate_phsB;       //配变负载率B
    float m_LoadRate_phsC;       //配变负载率C

    float m_MaxLoadRate;         //周期内配变负载率
    float m_MaxLoadRate_phsA;    //周期内配变负载率A
    float m_MaxLoadRate_phsB;    //周期内配变负载率B
    float m_MaxLoadRate_phsC;    //周期内配变负载率C

    float m_MaxLoad;         //周期内最大负荷
    float m_MaxLoad_phsA;    //周期内最大负荷a
    float m_MaxLoad_phsB;    //周期内最大负荷b
    float m_MaxLoad_phsC;    //周期内最大负荷c

    float m_TotResLoad;   //可开放容量    //m_TotResLoad   m_ocAnalyzer
    float m_ResLoad_phsA; //可开放容量A  //m_ResLoad_phsA  m_ocAnalyzer_phsA
    float m_ResLoad_phsB; //可开放容量B
    float m_ResLoad_phsC; //可开放容量C


private:
    CIIMutex	                   		 	m_cs;               //  备用
    CParamThread           		            m_Thread;           //  线程     

    CTimerCnt								m_T;				// 计时器 备用
	CTimerCnt								m_T_Imb_YX;				// 计时器 备用 相电流不平衡度遥信
    CTimerCnt								m_T_ZImb_YX;				// 计时器 备用 零线电流不平衡度遥信
    CTimerCnt								m_T_Imb_mild_YX;				// 轻度电流不平衡遥信
    CTimerCnt								m_T_Imb_middle_YX;				// 中度电流不平衡遥信
    CTimerCnt								m_T_Imb_severe_YX;				// 重度电流不平衡遥信


    friend class CParamThread;

};

#endif
