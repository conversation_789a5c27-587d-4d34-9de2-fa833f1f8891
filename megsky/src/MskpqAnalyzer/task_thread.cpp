#include <openssl/md5.h>
#include "mqtt_pub_inter.h"
#include "param_json.h"
#include "task_thread.h"
#include <cmath>
#include <iostream>
#include <sys/types.h>
#include <dirent.h>
#include <vector>
#include <string.h>

CDataObj::CDataObj(void)
{
    m_startFlag = false;
}

CDataObj::~CDataObj()
{
}

CTaskThread::CTaskThread(void)
    : m_pMng(NULL)
{
}

CTaskThread::~CTaskThread()
{
}

void CTaskThread::SetPackageAcquireManager(CTaskManager *p)
{
    m_pMng = p;
}

void CTaskThread::run(void)
{
    m_pMng->thread_prev();
    while (CSL_thread::IsEnableRun())
    {
        m_pMng->thread_func();
    }
    m_pMng->thread_exit();
}

std::string CTaskThread::ThreadName(void) const
{
    return std::string("MQTT 内部Broker线程");
}

void CTaskThread::RecordLog(const std::string &sMsg)
{
}

//***************************************************************
// 报文获取管理者
//***************************************************************
CTaskManager &CTaskManager::CreateInstance()
{
    static CTaskManager Mng;
    return Mng;
}

bool CTaskManager::Init(void)
{
    bool bRet(false);
 
    std::list<CFileObj>::iterator iter = CParamManager::CreateInstance().m_FileObjs.begin();
    for (; iter != CParamManager::CreateInstance().m_FileObjs.end(); ++iter)
    {
        CFileObj obj = *iter;
        std::string topic = obj.m_Topic + "+";
        CMqttClientInterManager::CreateInstance().m_topics.push_back(topic);
        CMqttClientInterManager::CreateInstance().agent_mqtt_msg_subscribe((char *)topic.c_str(), QOS);
    }

    n_ops = OPS_CHECK;  //1加密校验

    SMTimeInfo currenttime = ::ii_get_current_mtime();
    currenttime.nHour = 0;
    currenttime.nMinute = 0;
    currenttime.nSecond = 0;
    m_baseTime = CEPTime(currenttime);
    m_dDay = 0;
    m_dmin = 0;

    bRet = m_Thread.start();
    return bRet;
}

void CTaskManager::Exit(void)
{

    m_Thread.close();
    m_Thread.wait_for_end();
}

void CTaskManager::thread_prev(void)
{
}

void CTaskManager::thread_func(void)
{
    OnRun();
}

void CTaskManager::thread_exit(void)
{
}

void CTaskManager::OnRun(void)
{
    ii_sleep(100);

    switch (n_ops)
    {
    // 加密校验
    case OPS_CHECK:
        mskprintf(":1\n");
        MqttGetDevEsn();
        ii_sleep(1000);
        n_ops = OPS_INIT;  
        break;
        // 向操作系统注册
    case OPS_OS_SYSTEM:
        mskprintf(":2\n");
        MqttOS_REG();
        ii_sleep(1000);
        break;
    case OPS_INIT:
        mskprintf("设置模型\n");
        MqttPackSetModelInfo_PQA(); 
        ii_sleep(1000);
        break;
    case OPS_INIT_OK:
        mskprintf("注册guid\n");
        MqttPackDevRegister();
        ii_sleep(1000);
        break;
    case OPS_REG: 
        mskprintf("获取GUID\n");
        MqttPackGetGuid(); // 获取GUID
		//m_pdAnalyzer_T.StartCount();  
        ii_sleep(1000);
		//licong

        MqttPackGetRealTimeData();
        MqttPackGetParameter();
        ii_sleep(1000);
		m_pdAnalyzer_T.StartCount();  
		//
        break;        
    case OPS_REG_OK: // 获取GUID成功，获取一次数据
        mskprintf(":OPS_REG_OK\n");
        mskprintf("获取实时数据和定值\n");
        MqttPackGetRealTimeData();
        MqttPackGetParameter();
        ii_sleep(1000);
        m_pdAnalyzer_T.StartCount();        
        break;
    case OPS_IDLE: // 成功后，定时计算电能质量数据并发送        
        break;
    default:
        break;
    }
    
    // 成功后，定时计算电能质量数据并发送  
    if(m_pdAnalyzer_T.CalOvertime())
    {

        MqttPackGetRealTimeData();//定期从数据中心读取数据
        pdAnalyzer_analysis();
    }

    //交采计算 
	if(m_ocLoadRate_T.CalOvertime())//1秒计算一次
    {
        CParamManager::CreateInstance().Unpack_ocAnalyzerData(); //计算可开放性容量负载
        MqttPackSetRealTimeData_oc();  //周期上送
        MqttPackNotificationData_1_oc();
    }

    
	
    if(m_ocAnalyzer_T.CalOvertime())//每一分钟上报一次
    {
        ////终端累计运行时间
        MqttGetDevStatus();

        //判断是新的一分钟还是上一分钟
        SMTimeInfo now_octm = ii_get_current_mtime();
        mskprintf("%04d-%02d-%02d %02d:%02d:%02d:%03d\n",now_octm.nYear,now_octm.nMonth,now_octm.nDay,now_octm.nHour,now_octm.nMinute,now_octm.nSecond,now_octm.nMinute);

        if(now_octm.nMinute != m_lastTime_oc.nMinute)//按分钟计算配变负载率计算周期
        {
            m_dmin++;
            if(m_dmin == (int)CParamManager::CreateInstance().m_Param_value["LoadRate_CalcCyc"])  //计算周期 清零一次
            {
                //初始化
                CParamManager::CreateInstance().initocData();  
                m_dmin = 0;
            }           
        }
        if(now_octm.nDay != m_lastTime_oc.nDay)//按天计算可开放容量计算周期
        {
            m_dDay++;
            //可开放容量计算周期
            if(m_dDay == (int)CParamManager::CreateInstance().m_Param_value["ResLoad_CalcCyc"])
            {
                CParamManager::CreateInstance().initoc_Load_Data(); 
                m_dDay = 0;
            }              
        }
        //时间同步
        m_lastTime_oc = now_octm;
        //计算
        //CParamManager::CreateInstance().Unpack_ocAnalyzerData();
        //MqttPackSetRealTimeData_oc();  //周期上送
        //MqttPackNotificationData_1_oc();
        //m_T.StartCount();   
        
        /*
        MskpqAnalyzer/dataCenter/JSON/get/request/version             // 数据中心版本查询

        MskpqAnalyzer/dataCenter/JSON/set/request/model             // 数据中心模型设置
        MskpqAnalyzer/dataCenter/JSON/set/request/devRegister      // 数据中心设备注册 

        MskpqAnalyzer/dataCenter/JSON/get/request/guid              // guid查询
        MskpqAnalyzer/dataCenter/JSON/get/request/model             // 模型内容查询
        MskpqAnalyzer/dataCenter/JSON/get/request/parameter         // 定值查询
        MskpqAnalyzer/dataCenter/JSON/get/request/realtime          // 实时数据查询

        MskpqAnalyzer/smiOS/JSON/request/devInfo                         //获取设备信息   
        MskpqAnalyzer/smiOS/JSON/request/register                     //OS-system/注册
        MskpqAnalyzer/smiOS/JSON/request/keepAlive                      //应用保活消息
        MskpqAnalyzer/smiOS/JSON/request/devStatus                     //3.2.7获取设备状态

        //遥信
        
        

        acMeter/Broadcast/JSON/report/notification/TTU/   -->MskpqAnalyzer/acMeter/JSON/action/request/transTxFrm

        断路器实时数据，获取参数，下发参数   MskpqAnalyzer/acMeter/JSON/action/request/transTxFrm

        电表实时数据   dataCenter/MskpqAnalyzer/JSON/get/response/meterRealtime

        阶段1：系统启动和认证
        OPS_CHECK - 加密校验
        调用 MqttGetDevEsn() 获取设备ESN
        状态转换到 OPS_INIT
        OPS_OS_SYSTEM - 操作系统注册
        调用 MqttOS_REG() 向操作系统注册
        等待注册响应
        阶段2：设备初始化
        OPS_INIT - 设置数据模型
        调用 MqttPackSetModelInfo_PQA() 设置电能质量分析模型
        状态转换到 OPS_INIT_OK
        OPS_INIT_OK - 设备注册
        调用 MqttPackDevRegister() 注册设备GUID
        状态转换到 OPS_REG
        阶段3：数据获取和配置
        OPS_REG - 获取GUID和初始数据
        调用 MqttPackGetGuid() 获取设备GUID
        调用 MqttPackGetRealTimeData() 获取实时数据
        调用 MqttPackGetParameter() 获取参数定值
        启动分析定时器 m_pdAnalyzer_T.StartCount()
        OPS_REG_OK - 确认数据获取
        再次获取实时数据和参数
        启动分析定时器
        阶段4：正常运行
        OPS_IDLE - 空闲状态，进入周期性任务
        周期性任务执行
        高频任务（每秒）
        交采计算 (m_ocLoadRate_T.CalOvertime())
        计算可开放容量负载
        上送实时数据
        发送通知数据
        中频任务（电能质量分析周期）
        电能质量分析 (m_pdAnalyzer_T.CalOvertime())
        获取实时数据
        执行电能质量分析计算
        低频任务（每分钟）
        系统状态管理 (m_ocAnalyzer_T.CalOvertime())
        获取设备运行状态
        时间同步和周期管理
        负载率和可开放容量计算周期管理
        */
    }
}

void CTaskManager::Start(void)
{
    std::list<CDataObj *>::iterator itInit = m_dataObj.begin();
    for (; itInit != m_dataObj.end(); itInit++)
    {
        CDataObj *objd = *itInit;
        if (objd->m_startFlag)
        {
            MqttPackOnline(*itInit); // 一直发上线就可以了
            IEC_LOG_RECORD(eRunType, "guid (%s)online.", objd->m_guid.guid.c_str());
            ii_sleep(1000);
        }
    }
}

void CTaskManager::Stop(void)
{
}

bool CTaskManager::UnpackData(mqtt_data_info_s &real_data)
{
    neb::CJsonObject oJson;
    if (!oJson.Parse(real_data.msg_send))
    {
        IEC_LOG_RECORD(eErrType, "json_loadb real_data.msg_send failed.");
        return false;
    }

    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        if(!strstr(real_data.pubtopic, "/Broadcast/JSON/report/notification/"))
        {
            // mskprintf("unpack mqtt topic:%s.\r\n", real_data.pubtopic);
            // mskprintf("unpack mqtt msg:%s.\r\n", mssm.c_str());
        }        
    }

    C256String pub_topic;
    std::string appName = GetFirsAppName(real_data.pubtopic); // 截取 首字符
    // 获取token值
    const char *tokenStr = NULL;
    std::string stokenStr = UnpackToken(oJson);
    if (stokenStr.size() > 0)
    {
        tokenStr = stokenStr.c_str();
        //mskprintf("tokenStr:%s.\r\n", tokenStr);
    }

    // 解析模型设置成功
    if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/set/response/model"))
    {
        std::string status;
        oJson.Get("status", status);
        mskprintf("status = %s\n", status.c_str());
        if (status == "OK")
        {
            n_ops = OPS_INIT_OK;
        }
    }
    // 设备注册成功
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/set/response/devRegister"))
    {   
        std::string status;
        oJson.Get("status", status);
        mskprintf("status = %s\n", status.c_str());
        if (status == "OK")
        {
            n_ops = OPS_REG;
        }
    }
    // 设备guid查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/guid"))
    {
        neb::CJsonObject oBodyJson;
        oJson.Get("body", oBodyJson);
        mskprintf("oBodyJson = %s\n", oBodyJson.ToString().c_str());
        if (!oBodyJson.IsArray())
        {
            return false;
        }
        int asize = oBodyJson.GetArraySize();
        mskprintf("asize = %d\n", asize);
        bool flag_devguid = false;  
        for (int i = 0; i < asize; i++)
        {
            neb::CJsonObject body_data;
            if (!oBodyJson.Get(i, body_data))
            {
                return false;
            }

            mskprintf("body_data = %s\n", body_data.ToString().c_str());

            std::string model;
            std::string port;
            std::string addr;
            std::string desc;
            std::string guid;
            std::string dev;

            body_data.Get("model", model);
            body_data.Get("port", port);
            body_data.Get("addr", addr);
            body_data.Get("desc", desc);
            body_data.Get("guid", guid);
            body_data.Get("dev", dev);

            mskprintf("model = %s\n", model.c_str());
            mskprintf("port = %s\n", port.c_str());

            if (model == CParamManager::CreateInstance().m_devModel && port == CParamManager::CreateInstance().m_devPort && addr == CParamManager::CreateInstance().m_devAddr && desc == CParamManager::CreateInstance().m_devDesc)
            {
                flag_devguid = true;
                CParamManager::CreateInstance().m_devGuid = guid;
                CParamManager::CreateInstance().m_devDev = dev;
                n_ops = OPS_REG_OK;
                mskprintf("guid = %s\n", guid.c_str());
                mskprintf("dev = %s\n", dev.c_str());
                mskprintf("model = %s\n", model.c_str());

                char topic_str[256] = {0};
                // 订阅ADC数据变化上报主题
                snprintf(topic_str, 128, "acMeter/Broadcast/JSON/report/notification/%s/%s", model.c_str(), dev.c_str());
                CMqttClientInterManager::CreateInstance().agent_mqtt_msg_subscribe(topic_str, QOS);
                mskprintf("subscribe: %s\r\n", topic_str);
                break;
            }
        }

        //未查询到guid,注册guid
        if(!flag_devguid)
        {
            MqttPackDevRegister();
        }
    }
    // 模型内容查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/model"))
    {
        //dataCenter/MskpqAnlayzer/JSON/get/response/model
        /* if (UnpackDevModelData(oJson))
        {
            n_ops = OPS_REG_OK;
        } */
    }
    // 定值内容查询 // 定值变更通知  dataCenter/Broadcast/JSON/report/notification/parameter
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/parameter")
    || strstr(real_data.pubtopic, "dataCenter/Broadcast/JSON/report/notification/parameter")||strstr(real_data.pubtopic, "MskIEC104/dataCenter/JSON/set/request/parameter"))
    {
		IEC_LOG_RECORD(eRunType,"the fist get parameter of datacenter   ");
        mskprintf("parameter\n");
        UnpackDevParameterData(oJson);
        n_ops = OPS_IDLE;
    }   
    // 实时数据查询
    else if (strstr(real_data.pubtopic, CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/realtime"))
    {
        mskprintf("realtime\n");
        UnpackRealData(oJson);
        n_ops = OPS_IDLE;
    }
    /* 变化上报主题 */
    else if (strstr(real_data.pubtopic, "/Broadcast/JSON/report/notification/"))
    {
        mskprintf("notification\n");
        std::string datatype;
        oJson.Get("datatype", datatype);
        if (datatype == "0") // 遥测，计算
        {
            UnpackNotificationData(oJson);
        }
    }
    //获取设备ESN,用于注册码解码
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devInfo"))
    {
        neb::CJsonObject body;
        oJson.Get("body", body);

        std::string devesn;
        body.Get("esn", devesn);
        //body.Get("osVer", devesn); // esn 目前是空的，用osVer代替
        mskprintf("esn:%s\n", devesn.c_str());
        char currentCode[4096] = ""; // 注册码
        CalMachinecode((char *)devesn.c_str(), currentCode);
        //mskprintf("code: %s\n", currentCode);

        // 文件路径
        std::vector<std::string> file_name;
        std::string path = "/data/app/common";
        GetFileNames(path, file_name);
        std::string filepathall;
        for (int i = 0; i < file_name.size(); i++)
        {
            // cout << file_name[i] << endl;
            filepathall += file_name[i];
        }
        std::string::size_type idx;
        idx = filepathall.find(currentCode); // 在a中查找b.
        if (idx == std::string::npos)             // 不存在。
        {
            mskprintf("Unauthorized equipment! \n");
            IEC_LOG_RECORD(eErrType, "Unauthorized equipment! code:");
            exit(0);
        }
        else
        {
            mskprintf("authorized equipment! code:\n");
            n_ops = OPS_OS_SYSTEM;
        }
    }
    //获取设备累计运行时间
    else if (strstr(real_data.pubtopic,CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/devStatus"))//3.2.7获取设备状态
    {
        neb::CJsonObject body;
        oJson.Get("body", body);

        int devRunTime;
        body.Get("devRunTime", devRunTime);        
        CParamManager::CreateInstance().m_pdAnalyzer_value["TTU_Run_Tm"] = devRunTime/60.0;

        mskprintf("@@@TTU_Run_Tm: %f\n",CParamManager::CreateInstance().m_pdAnalyzer_value["TTU_Run_Tm"]);
        
    }
    // 注册
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/response/register"))
    {
        oJson.Get("statusCode", CParamManager::CreateInstance().m_OS_statusCode);
        oJson.Get("statusDesc", CParamManager::CreateInstance().m_OS_statusDesc);

        mskprintf("statusCode:%d  statusDesc: %s\n", CParamManager::CreateInstance().m_OS_statusCode, CParamManager::CreateInstance().m_OS_statusDesc.c_str());
        n_ops = OPS_INIT;
        return true;
    }
    // 保活
    else if (strstr(real_data.pubtopic, CFG_OS_NAME "/" CFG_APP_NAME "/JSON/request/keepAlive"))
    {
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, "123");
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return false;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", 123);
        oJson.Add("timestamp", jsheader.timestamp);
        oJson.Add("statusCode", CParamManager::CreateInstance().m_OS_statusCode);
        oJson.Add("statusDesc", CParamManager::CreateInstance().m_OS_statusDesc);
        char pub_topic[100] = CFG_APP_NAME "/OS-system/JSON/response/keepAlive";
        std::string mssm = oJson.ToString();
        if (mssm.size() > 0)
        {
            mskprintf("find dev list topic:%s\r\n", pub_topic);
            mskprintf("find dev list msg:%s\r\n", mssm.c_str());
            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char *)(mssm.c_str()), (char *)pub_topic, 0);
        }
        return true;
    }
    else if (strstr(real_data.pubtopic,  "acMeter/" CFG_APP_NAME "/JSON/action/response/transTxFrm"))
    {
        std::string TransType;
        oJson.Get("TransType", TransType);
        mskprintf("TransType:%s\n",TransType);
        std::string code;
        oJson.Get("code",code);
        if(code != "0")
        {
            mskprintf("返回结果失败\n");
            return false;
        }
        if (TransType == "dataGet")  
        {
            mskprintf("ADC realtime\n");
            UnpackRealData(oJson);
        }
        else if(TransType == "cmdGet")
        {
            IEC_LOG_RECORD(eRunType,"the fist get parameter from acMeter ");
            mskprintf("parameter\n");
            UnpackDevParameterData(oJson);
        }
        else if(TransType == "cmdSet")
        {
            mskprintf("cmdset\n");
            mskprintf("下发参数成功\n");
        }
        n_ops = OPS_IDLE;
    }
    if(strstr(real_data.pubtopic, MCCB_NAME "/" CFG_APP_NAME "JSON/action/response/transTxFrm"))
    {
        std::string TransType;
        oJson.Get("TransType", TransType);
        mskprintf("TransType:%s\n",TransType);
        std::string code;
        oJson.Get("code",code);
        if(code != "0")
        {
            mskprintf("返回结果失败\n");
            return false;
        }
        if (TransType == "dataGet")  
        {
            mskprintf("MCCB realtime\n");
            UnpackRealData(oJson);
        }
        else if(TransType == "cmdGet")
        {
            IEC_LOG_RECORD(eRunType,"the fist get parameter from MCCB ");
            mskprintf("parameter\n");
            UnpackDevParameterData(oJson);
        }
        else if(TransType == "cmdSet")
        {
            mskprintf("cmdset\n");
            mskprintf("下发参数成功\n");
        }
        n_ops = OPS_IDLE;
    }
    //dataCenter/{app}/JSON/get/response/meterRealtime 电表实时数据 
    if(strstr(real_data.pubtopic,CFG_DATA_CENTER_NAME "/" CFG_APP_NAME "/JSON/get/response/meterRealtime" ))
    {
        std::string meterdev;
        oJson.Get("dev",meterdev);
        mskprintf("dev:",meterdev);
        UnpackRealData(oJson);
        n_ops = OPS_IDLE;
    }
    //mskprintf("n_ops = %d\n", n_ops);
    return true;
}

// 解析模型数据 
bool CTaskManager::UnpackDevModelData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevModelData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevModelData body is not Array.");
        return false;
    }        

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackDevModelData body data get error i = %d.\n", i);
            return false;
        }
        std::string modelname;
        obj_data.Get("model", modelname);
        
        

        mskprintf("modelname = %s, %s\n", modelname.c_str(), CParamManager::CreateInstance().m_devModel.c_str());

        if (modelname != CParamManager::CreateInstance().m_devModel)
        {
            continue;
        }
        
        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackDevModelData body data get error j = %d.\n", j);
                return false;
            }
            
            std::string modelvaluename;
            obj_value.Get("name", modelvaluename);
            CParamManager::CreateInstance().m_ADC_value[modelvaluename] = 0.0f;
        }
        
    }

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    }

    if (CParamManager::CreateInstance().m_ADC_value.size() > 0)
    {
        return true;
    }
    else
    {
        return false;
    }
}

// 解析定值数据
bool CTaskManager::UnpackDevParameterData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body is not Array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackDevParameterData body data get error i = %d.\n", i);
            return false;
        }
        std::string devName;
        obj_data.Get("dev", devName);

        mskprintf("modelname = %s, %s\n", devName.c_str(), CParamManager::CreateInstance().m_devDev.c_str());

        if (devName != CParamManager::CreateInstance().m_devDev)
        {
            continue;
        }

        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackDevParameterData body data get error j = %d.\n", j);
                IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body data get error j = %d.\n", j);
                return false;
            }

            std::string valuename;
            obj_value.Get("name", valuename);

            std::string value;
            obj_value.Get("val", value);
            float _value = atof(value.c_str());
            mskprintf("valuename = %s, value =%s\n", valuename.c_str(), value.c_str());
			IEC_LOG_RECORD(eRunType, "set para valuename = %s, value =%s", valuename.c_str(), value.c_str());
            if (!CParamManager::CreateInstance().SetConstParam(valuename, _value))
            {
                IEC_LOG_RECORD(eErrType, "UnpackDevParameterData body is not Array.");
            }
        }
    }
    //n_ops = OPS_REG_OK;
    return 0;
}

// 解析初始化数据
bool CTaskManager::UnpackRealData(neb::CJsonObject &obj)
{
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }

    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string devname;
        obj_data.Get("dev", devname);

        mskprintf("devname = %s, %s\n", devname.c_str(), CParamManager::CreateInstance().m_devDev.c_str());

        if (devname != CParamManager::CreateInstance().m_devDev)
        {
            continue;
        }
        
        neb::CJsonObject obj_model_data;
        obj_data.Get("body", obj_model_data);
        int bsize = obj_model_data.GetArraySize();
        for (int j = 0; j < bsize; j++)
        {
            neb::CJsonObject obj_value;
            if (!obj_model_data.Get(j, obj_value))
            {
                mskprintf("UnpackRealData body data get error j = %d.\n", j);
                return false;
            }

            std::string name;
            obj_value.Get("name", name);

            std::string value;
            obj_value.Get("val", value);
            float _value = atof(value.c_str());
            mskprintf("name = %s, value =%s\n", name.c_str(), value.c_str());

            if (name.compare("PhV_phsA")  == 0|| name.compare("PhV_phsB")  == 0|| name.compare("PhV_phsC") == 0|| name.compare("SeqV_c0")  == 0
                || name.compare("A_phsA")  == 0|| name.compare("A_phsB") == 0|| name.compare("A_phsC") == 0 || name.compare("A_phsN") == 0  
                || name.compare("SeqA_c0")  == 0|| name.compare("ResA")  == 0 || name.compare("Hz")  == 0
                || name.compare("PhPF_phsA")  == 0 || name.compare("PhPF_phsB") == 0 || name.compare("PhPF_phsC")  == 0|| name.compare("TotPF") == 0
                || name.compare("PhVA_phsA") == 0 || name.compare("PhVA_phsB") == 0 || name.compare("PhVA_phsC")  == 0 || name.compare("TotVA") == 0
                || name.compare("PhVAng_phsA") == 0 || name.compare("PhVAng_phsB") == 0 || name.compare("PhVAng_phsC")  == 0 ||name.compare("SeqA_c1")  == 0||name.compare("SeqA_c2")  == 0||name.compare("SeqV_c1")  == 0||name.compare("SeqV_c2")  == 0)
            {
                CParamManager::CreateInstance().SetAdcData(name, _value);
            }
        }
    }

    //CParamManager::CreateInstance().Unpack_pdAnalyzerData();//计算
   /*  // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } */
    return true;
}

//数据变化上报
bool CTaskManager::UnpackNotificationData(neb::CJsonObject &obj)
{    
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackNotificationData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackNotificationData body is not Array.");
        return false;
    }

    bool ADC_PQ_flag = false;
	bool ADC_OC_flag = false;
    int asize = obj_body.GetArraySize();
    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackNotificationData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string value;
        obj_data.Get("name", name);
        obj_data.Get("val", value);
        //mskprintf("name = %s, value =%s\n", name.c_str(), value.c_str());
        //交采更新
         if (name.compare("PhV_phsA")  == 0|| name.compare("PhV_phsB")  == 0|| name.compare("PhV_phsC") == 0|| name.compare("SeqV_c0")  == 0
                || name.compare("A_phsA")  == 0|| name.compare("A_phsB") == 0|| name.compare("A_phsC") == 0 || name.compare("A_phsN") == 0  || name.compare("SeqA_c0")  == 0 || name.compare("ResA")  == 0
                || name.compare("Hz")  == 0
                || name.compare("PhPF_phsA")  == 0 || name.compare("PhPF_phsB") == 0 || name.compare("PhPF_phsC")  == 0|| name.compare("TotPF") == 0
                || name.compare("PhVA_phsA") == 0 || name.compare("PhVA_phsB") == 0 || name.compare("PhVA_phsC")  == 0 || name.compare("TotVA") == 0
                || name.compare("PhVAng_phsA") == 0 || name.compare("PhVAng_phsB") == 0 || name.compare("PhVAng_phsC")  == 0 ||name.compare("SeqA_c1")  == 0||name.compare("SeqA_c2")  == 0||name.compare("SeqV_c1")  == 0||name.compare("SeqV_c2")  == 0)
        {
            float _value = atof(value.c_str());
            if (!CParamManager::CreateInstance().SetAdcData(name, _value))
            {
                IEC_LOG_RECORD(eErrType, "UnpackNotificationData SetAdcData is failed.");
            }
            
        }

        if(name.compare("PhV_phsA") == 0|| name.compare("PhV_phsB") == 0||name.compare("PhV_phsC") == 0
            || name.compare("A_phsA") == 0||name.compare("A_phsB") == 0
            ||name.compare("A_phsC") == 0||name.compare("A_phsN") == 0||name.compare("A_phsN") == 0)
        {
            ADC_PQ_flag =  true;
        }
        
		if (name.compare("PhVA_phsA")==0 || name.compare("PhVA_phsB")==0 || name.compare("PhVA_phsC")==0 || name.compare("TotVA")==0)
        {
            ADC_OC_flag = true;
        }
    }

    if(ADC_PQ_flag)
    {
        CParamManager::CreateInstance().Unpack_pdAnalyzerImb();//计算 
    }
    //CParamManager::CreateInstance().Unpack_pdAnalyzerData();//计算

    // 打印变量信息
    /* std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_ADC_value.begin(); iter != CParamManager::CreateInstance().m_ADC_value.end(); iter++)
    {
        mskprintf("name =  %s , value = %f\n", iter->first.c_str(), iter->second);
    } */
    return true;
}

bool CTaskManager::UnpackRealData(CDataObj *dobj, neb::CJsonObject obj)
{
    bool b(false);
    if (!dobj->m_startFlag)
    {
        dobj->m_startFlag = true;
        dobj->m_startTime = ii_get_current_mtime();
    }

    if (dobj == NULL)
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData dobj = NULL.");
        return false;
    }
    bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body = NULL.");
        return false;
    }
    if (!obj_body.IsArray())
    {
        IEC_LOG_RECORD(eErrType, "UnpackRealData body is not Array.");
        return false;
    }
    int asize = obj_body.GetArraySize();
    // 清空当前的变化name
    dobj->m_SpontObj.m_Spontnames.clear();

    for (int i = 0; i < asize; i++)
    {
        neb::CJsonObject obj_data;
        if (!obj_body.Get(i, obj_data))
        {
            mskprintf("UnpackRealData body data get error i = %d.\n", i);
            return false;
        }
        std::string name;
        std::string val;
        std::string stype;
        bRet &= obj_data.Get("name", name);
        bRet &= obj_data.Get("val", val);

        if (name.size() > 0)
        {
            dobj->m_RealDatas[name] = val;
            obj_data.Get("type", stype);
            if (stype.compare("cycle") == 0)
            { // 周期数据赋值
                continue;
            }
            mskprintf("yx change name = (%s),val = (%s).\n", name.c_str(), val.c_str());
            // 如果name在遥信中则发送变化
            std::string serveiceName;
            if (IsFindDiscrete(dobj, name, serveiceName))
            {
                dobj->m_SpontObj.m_Spontnames.push_back(name);
                dobj->m_SpontObj.m_serviceName = serveiceName;
                b = true;
            }
        }
    }
    if (b)
    {
        MqttPackSpontData(dobj);
    }
    return bRet;
}

std::string CTaskManager::UnpackToken(neb::CJsonObject obj)
{
     std::string tokenStr;
    if (obj.Get("token", tokenStr))
    {
        return tokenStr;
    }

    int token;
    if (obj.Get("token", token))
    {
        return std::to_string(token);
    }
    return NULL;
}

void CTaskManager::MqttPackEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    const char *pub_topic = CFG_APP_NAME "/get/request/esdk/deviceInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic, 0);
    }
}

void CTaskManager::MqttGetDevEsn()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);   

    const char *pub_topic = CFG_APP_NAME "/OS-system/JSON/request/devInfo";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttOS_REG()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", 1);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject body;
    body.Add("name", CParamManager::CreateInstance().m_appName);
    body.Add("version", CParamManager::CreateInstance().m_appVersion);
    body.Add("releaseDate", CParamManager::CreateInstance().m_appRealseDate);
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/OS-system/JSON/request/register";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetGuid()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("model", CParamManager::CreateInstance().m_devModel);
    body.Add("port", CParamManager::CreateInstance().m_devPort);
    body.Add("addr", CParamManager::CreateInstance().m_devAddr);
    body.Add("desc", CParamManager::CreateInstance().m_devDesc);
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/guid";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetModelInfo()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    neb::CJsonObject body;
    body.Add("TTU");
    oJson.Add("body", body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

// 获取一次定值  MskpqAnalyzer/dataCenter/JSON/get/request/parameter
void CTaskManager::MqttPackGetParameter()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("dev", CParamManager::CreateInstance().m_devDev);
    body.Add("totalcall", "1");
    body.AddEmptySubArray("body");
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/parameter";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    } 
}
void CTaskManager::MqttPackGetParameter_DTA()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("dev",CParamManager::CreateInstance().m_devDev);
    oJson.Add("TransType","dataGet");
    oJson.Add("TimeOut",10);
    oJson.Add("ByteTimeOut",10);
    oJson.Add("Prio",3);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject bodybogy;

    oJson.Add("body",bodybogy);
    oJson["body"].Add(bodybogy);

    const char *pub_topic;
    if(CParamManager::CreateInstance().m_devModel == "ADC")
    {
        pub_topic = CFG_APP_NAME "/acMeter/JSON/action/request/transTxFrm";
    }
    if(CParamManager::CreateInstance().m_devModel == "MCCB")
    {
        pub_topic = CFG_APP_NAME "/" MCCB_NAME "/JSON/action/request/transTxFrm";
    }
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//获取实时数据 MskpqAnalyzer/dataCenter/JSON/get/request/realtime
void CTaskManager::MqttPackGetRealTimeData()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }


    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    

    neb::CJsonObject body;
    body.Add("dev", CParamManager::CreateInstance().m_devDev);
    body.Add("totalcall", "0");


    neb::CJsonObject bodybogy;
    bodybogy.Add("PhV_phsA");
    bodybogy.Add("PhV_phsB");
    bodybogy.Add("PhV_phsC");
    bodybogy.Add("SeqV_c0");
    bodybogy.Add("SeqV_c1");
    bodybogy.Add("SeqV_c2");
    bodybogy.Add("A_phsA");
    bodybogy.Add("A_phsB");
    bodybogy.Add("A_phsC");
    bodybogy.Add("A_phsN");
    bodybogy.Add("SeqA_c0");
    bodybogy.Add("SeqA_c1");
    bodybogy.Add("SeqA_c2");
    bodybogy.Add("ResA");
    bodybogy.Add("Hz");
    bodybogy.Add("PhPF_phsA");
    bodybogy.Add("PhPF_phsB");
    bodybogy.Add("PhPF_phsC");
    bodybogy.Add("TotPF");
    bodybogy.Add("PhVA_phsA");
    bodybogy.Add("PhVA_phsB");
    bodybogy.Add("PhVA_phsC");
    bodybogy.Add("TotVA");
    body.Add("body",bodybogy);
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/get/request/realtime";
    // const char *pub_topic;
    // if(CParamManager::CreateInstance().m_devModel == "Meter")
    // {
    //     pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "JSON/get/request/meterRealtime";
    // }
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackGetRealTimeData_DTA()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("dev",CParamManager::CreateInstance().m_devDev);
    oJson.Add("TransType","dataGet");
    oJson.Add("TimeOut",10);
    oJson.Add("ByteTimeOut",10);
    oJson.Add("Prio",3);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject bodybogy;
    bodybogy.Add("PhV_phsA");
    bodybogy.Add("PhV_phsB");
    bodybogy.Add("PhV_phsC");
    bodybogy.Add("SeqV_c0");
    bodybogy.Add("SeqV_c1");
    bodybogy.Add("SeqV_c2");
    bodybogy.Add("A_phsA");
    bodybogy.Add("A_phsB");
    bodybogy.Add("A_phsC");
    bodybogy.Add("A_phsN");
    bodybogy.Add("SeqA_c0");
    bodybogy.Add("SeqA_c1");
    bodybogy.Add("SeqA_c2");
    bodybogy.Add("ResA");
    bodybogy.Add("Hz");
    bodybogy.Add("PhPF_phsA");
    bodybogy.Add("PhPF_phsB");
    bodybogy.Add("PhPF_phsC");
    bodybogy.Add("TotPF");
    bodybogy.Add("PhVA_phsA");
    bodybogy.Add("PhVA_phsB");
    bodybogy.Add("PhVA_phsC");
    bodybogy.Add("TotVA");
    oJson.Add("body",bodybogy);
    oJson["body"].Add(bodybogy);

    const char *pub_topic;
    if(CParamManager::CreateInstance().m_devModel == "ADC")
    {
        pub_topic = CFG_APP_NAME "/acMeter/JSON/action/request/transTxFrm";
    }
    if(CParamManager::CreateInstance().m_devModel == "MCCB")
    {
        pub_topic = CFG_APP_NAME "/" MCCB_NAME "/JSON/action/request/transTxFrm";
    }
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }   
}

//获取设备状态  MskpqAnalyzer/OS_system/JSON/request/devStatus
void CTaskManager::MqttGetDevStatus()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", 123);
    oJson.Add("timestamp", jsheader.timestamp);
    
    
    const char *pub_topic = CFG_APP_NAME "/" CFG_OS_NAME "/JSON/request/devStatus";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

// 电能质量分析模型  /MskpqAnalyzer/dataCenter/JSON/set/request/model 设置数据模型 
void CTaskManager::MqttPackSetModelInfo_PQA()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }
    
    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("model", CParamManager::CreateInstance().m_devModel);
    
    neb::CJsonObject body;
    for (int i = 0; i < CParamManager::CreateInstance().m_vmodel.size(); i++)
    {
        neb::CJsonObject bodyitem;
        bodyitem.Add("name", CParamManager::CreateInstance().m_vmodel[i].name);
        bodyitem.Add("type", CParamManager::CreateInstance().m_vmodel[i].type);
        bodyitem.Add("unit", CParamManager::CreateInstance().m_vmodel[i].unit);
        bodyitem.Add("deadzone", CParamManager::CreateInstance().m_vmodel[i].deadzone);
        bodyitem.Add("ratio", CParamManager::CreateInstance().m_vmodel[i].ratio);
        bodyitem.Add("isReport", CParamManager::CreateInstance().m_vmodel[i].isReport);
        bodyitem.Add("userdefine", CParamManager::CreateInstance().m_vmodel[i].userdefine);

        body.Add(bodyitem);
    }
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/set/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

void CTaskManager::MqttPackSetModelInfo_MCCB()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("model", CParamManager::CreateInstance().m_MCCBdevModel);

    
    neb::CJsonObject body;
    for (int i = 0; i < CParamManager::CreateInstance().m_vmodel.size(); i++)
    {
        std::string name = CParamManager::CreateInstance().m_vmodel[i].name;
        if(name == " ")
        {

        }
        neb::CJsonObject bodyitem;
        bodyitem.Add("name", CParamManager::CreateInstance().m_vmodel[i].name);
        bodyitem.Add("type", CParamManager::CreateInstance().m_vmodel[i].type);
        bodyitem.Add("unit", CParamManager::CreateInstance().m_vmodel[i].unit);
        bodyitem.Add("deadzone", CParamManager::CreateInstance().m_vmodel[i].deadzone);
        bodyitem.Add("ratio", CParamManager::CreateInstance().m_vmodel[i].ratio);
        bodyitem.Add("isReport", CParamManager::CreateInstance().m_vmodel[i].isReport);
        bodyitem.Add("userdefine", CParamManager::CreateInstance().m_vmodel[i].userdefine);

        body.Add(bodyitem);
    }
    oJson.Add("body",body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/set/request/model";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}





////可开放容量分析模型
void CTaskManager::MqttPackSetModelInfo_OCFc()
{

}

// 注册  MskpqAnalyzer/dataCenter/JSON/set/request/devRegister
void CTaskManager::MqttPackDevRegister()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.AddEmptySubArray("body");

    neb::CJsonObject body;
    body.Add("model", CParamManager::CreateInstance().m_devModel);
    body.Add("port", CParamManager::CreateInstance().m_devPort);
    body.Add("addr", CParamManager::CreateInstance().m_devAddr);
    body.Add("desc", CParamManager::CreateInstance().m_devDesc);
    body.Add("manuID", CParamManager::CreateInstance().m_devmanuID);
    body.Add("manuName", CParamManager::CreateInstance().m_devmanuName);
    body.Add("ProType", CParamManager::CreateInstance().m_devProType);
    body.Add("deviceType", CParamManager::CreateInstance().m_devdeviceType);
    body.Add("isReport", CParamManager::CreateInstance().m_devisReport);
    body.Add("nodeID", CParamManager::CreateInstance().m_devnodeID);
    body.Add("productID", CParamManager::CreateInstance().m_devproductID);
    
    oJson["body"].Add(body);

    const char *pub_topic = CFG_APP_NAME "/" CFG_DATA_CENTER_NAME "/JSON/set/request/devRegister";
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    
}



//上报不平衡度
bool CTaskManager::MqttPack_pdAnalyzerImb()
{
    mskprintf("\n上报一次不平衡度数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return false;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   
    

    //电压不平衡度
    neb::CJsonObject sbodydata;
    sbodydata.Add("name", "ImbNgV");
    sbodydata.Add("id", "1");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgV"]*100));
    sbodydata.Add("unit", "");
    sbodydata.Add("quality", "0");
    sbodydata.Add("timestamp", jsheader.timestamp);
    body.Add(sbodydata);

    //电流不平衡度
    sbodydata.Replace("name", "ImbNgA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgA"]*100));
    body.Add(sbodydata);

    //负荷不平衡度
    sbodydata.Replace("name", "ImbNgLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value["ImbNgLoad"]*100));
    body.Add(sbodydata);

    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

    mskprintf("\n上报遥测数据结束\n");

    return true;
}

bool CTaskManager::MqttPack_pdAnalyzerData()
{
    // 2、数据变化上报，104接收；2.4 极值推送，
    MqttPackNotificationData_4();

    // 2、数据变化上报，104接收；2.5  日月合格率推送
    MqttPackNotificationData_5();

    // 2、数据变化上报，104接收；2.6  日月越限推送
    MqttPackNotificationData_6();

    // 2、数据变化上报，104接收；2.7  日重载、日过载推送
    MqttPackNotificationData_7();

    return false;
}

//获取实时数据 
void CTaskManager::MqttPackSetRealTimeData()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single");    //实时数据
    
    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_pdAnalyzer_value.begin(); iter != CParamManager::CreateInstance().m_pdAnalyzer_value.end(); iter++)
    {
        // 跳过初始值
        float fvlaue = iter->second;
        if (fvlaue > 9998 || fvlaue < -9998)
        {
            continue;
        }
        
        std::string name = iter->first;

        //电压偏差  
        if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0 || name.compare("ImbA0") == 0 
        || name.compare("ImbA2") == 0|| name.compare("ImbV0") == 0|| name.compare("ImbV2") == 0)
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME "/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        
    }
}

//可开放容量分析实时数据推送
void CTaskManager::MqttPackSetRealTimeData_oc()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single");    //实时数据
    neb::CJsonObject body;   

    //组配变负载率
    neb::CJsonObject sbodydata;        
    sbodydata.Add("name", "LoadRate");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_LoadRate));//licong
    sbodydata.Add("quality", "0");
    sbodydata.Add("secret", "1");
    sbodydata.Add("timestamp", jsheader.timestamp);    
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsC));
    body.Add(sbodydata);

    //组最大负荷 
    sbodydata.Replace("name","LoadMax");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad));
    body.Add(sbodydata);

   /*  sbodydata.Replace("name","LoadMax_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsC));
    body.Add(sbodydata); */

    //组可开放容量   
    sbodydata.Replace("name","TotResLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_TotResLoad));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsC));
    body.Add(sbodydata);
  
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}
// 推送一次实时数据-遥信
void CTaskManager::MqttPackSetRealTimeData_yx(std::string name)
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("data_row", "single"); // 实时数据

    neb::CJsonObject body;

    // 打印变量信息
    neb::CJsonObject sbodydata;
    sbodydata.Add("name", name.c_str());
    if (name.compare("Hz") == 0)
    {
        sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_ADC_value[name]));
    }
    else
    {
        sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value_YX[name]));
    }
    sbodydata.Add("quality", "0");
    sbodydata.Add("secret", "1");
    sbodydata.Add("timestamp", jsheader.timestamp);
    body.Add(sbodydata);

    oJson.Add("body", body);

    // char pub_topic[256] = {0};
    // snprintf(pub_topic, 128, CFG_APP_NAME "/dataCenter/JSON/set/request/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string pub_topic = CFG_APP_NAME;
    pub_topic += "/dataCenter/JSON/set/request/";
    pub_topic += CParamManager::CreateInstance().m_devModel;
    pub_topic += "/";
    pub_topic += CParamManager::CreateInstance().m_devDev;


    std::string mssm = oJson.ToString();

    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), mssm.size(), mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)(pub_topic.c_str()), pub_topic.size(), pub_topic.size());
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//交采可开放性容量分析遥测数据 广播  CFG_APP_NAME"/Broadcast/JSON/report/notification
void CTaskManager::MqttPackNotificationData_1_oc() 
{
    mskprintf("\n上报一次可开放容量分析遥测数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   

    //组配变负载率
    neb::CJsonObject sbodydata;        
    sbodydata.Add("name", "LoadRate");
    sbodydata.Add("id", "1");
    sbodydata.Add("val", std::to_string(CParamManager::CreateInstance().m_LoadRate*100));//licong
    sbodydata.Add("unit", "");
    sbodydata.Add("quality", "0");
    sbodydata.Add("timestamp", jsheader.timestamp);    
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadRate_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_LoadRate_phsC));
    body.Add(sbodydata);

    //组最大负荷 
    sbodydata.Replace("name","LoadMax");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad));
    body.Add(sbodydata);

    /* sbodydata.Replace("name","LoadMax_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","LoadMax_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_MaxLoad_phsC));
    body.Add(sbodydata); */

    //组可开放容量   
    sbodydata.Replace("name","TotResLoad");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_TotResLoad));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsA");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsA));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsB");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsB));
    body.Add(sbodydata);

    sbodydata.Replace("name","ResLoad_phsC");
    sbodydata.Replace("val", std::to_string(CParamManager::CreateInstance().m_ResLoad_phsC));
    body.Add(sbodydata);
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

    mskprintf("\n上报遥测数据结束\n");

}

//推送一次 数据变化上报 遥测数据
void CTaskManager::MqttPackNotificationData_1()
{
    mskprintf("\n上报一次遥测数据\n");
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "0");    //遥测数据

    neb::CJsonObject body;   

    // 打印变量信息
    std::map<std::string, float>::iterator iter;
    for (iter = CParamManager::CreateInstance().m_pdAnalyzer_value.begin(); iter != CParamManager::CreateInstance().m_pdAnalyzer_value.end(); iter++)
    { 
        //跳过初始值
        float fvlaue = iter->second;
        if(fvlaue > 9998 || fvlaue <-9998)
        {
            continue;
        }
        // neb::CJsonObject sbodydata;        
        // sbodydata.Add("name", iter->first.c_str());
        // sbodydata.Add("id", "1");
        // sbodydata.Add("val", std::to_string(iter->second));
        // sbodydata.Add("unit", "");
        // sbodydata.Add("quality", "0");        
        // sbodydata.Add("timestamp", jsheader.timestamp);        
        // body.Add(sbodydata); 
	// Licong
 		std::string name = iter->first;
		 if (name.compare("ImbNgV") == 0 || name.compare("ImbNgA") == 0 || name.compare("ImbNgLoad") == 0 ||name.compare("LoadRate") == 0  )
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(100*iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
        else
        {
            neb::CJsonObject sbodydata;
            sbodydata.Add("name", iter->first.c_str());
            sbodydata.Add("val", std::to_string(iter->second));
            sbodydata.Add("quality", "0");
            sbodydata.Add("secret", "1");
            sbodydata.Add("timestamp", jsheader.timestamp);
            body.Add(sbodydata);
        }
	// licong
    }
   
    oJson.Add("body",body);
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

    mskprintf("\n上报遥测数据结束\n");
}

//推送一次 数据变化上报 遥信数据
void CTaskManager::MqttPackNotificationData_2(std::string name)
{
    mskprintf("\n上报一次遥信数据  %s\n",name.c_str());
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "1");    //遥信数据 
    oJson.AddEmptySubArray("body");
    
    neb::CJsonObject body;    
    body.Add("name",name);
    body.Add("id","");
    body.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value_YX[name]));
    body.Add("unit","");
    body.Add("quality","");
    body.Add("timestamp",jsheader.timestamp);
    oJson["body"].Add(body);

    
    
    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
    MqttPackSetRealTimeData_yx(name);
    mskprintf("\n上报遥信数据结束\n");
}

//极值推送
void CTaskManager::MqttPackNotificationData_4()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "3");    //极值数据

    neb::CJsonObject bodyMax;   
    bodyMax.Add("type","0");//极大值    
    neb::CJsonObject dataMax; 
    JsonPackNameData(dataMax,"PhV_phsA","PhVMaxDay_phsA",CParamManager::CreateInstance().m_PhVMaxDay_phsA_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsB","PhVMaxDay_phsB",CParamManager::CreateInstance().m_PhVMaxDay_phsB_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsC","PhVMaxDay_phsC",CParamManager::CreateInstance().m_PhVMaxDay_phsC_TIME.c_str());
/*     JsonPackNameData(dataMax,"PhV_phsA","PhVMaxMon_phsA",CParamManager::CreateInstance().m_PhVMaxMon_phsA_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsB","PhVMaxMon_phsB",CParamManager::CreateInstance().m_PhVMaxMon_phsB_TIME.c_str());
    JsonPackNameData(dataMax,"PhV_phsC","PhVMaxMon_phsC",CParamManager::CreateInstance().m_PhVMaxMon_phsC_TIME.c_str()); */
    bodyMax.Add("data",dataMax);

    neb::CJsonObject bodyMin;   
    bodyMin.Add("type","1");//极小值
    neb::CJsonObject dataMin; 
    JsonPackNameData(dataMin,"PhV_phsA","PhVMinDay_phsA",CParamManager::CreateInstance().m_PhVMinDay_phsA_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsB","PhVMinDay_phsB",CParamManager::CreateInstance().m_PhVMinDay_phsB_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsC","PhVMinDay_phsC",CParamManager::CreateInstance().m_PhVMinDay_phsC_TIME.c_str());
/*     JsonPackNameData(dataMin,"PhV_phsA","PhVMinMon_phsA",CParamManager::CreateInstance().m_PhVMinMon_phsA_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsB","PhVMinMon_phsB",CParamManager::CreateInstance().m_PhVMinMon_phsB_TIME.c_str());
    JsonPackNameData(dataMin,"PhV_phsC","PhVMinMon_phsC",CParamManager::CreateInstance().m_PhVMinMon_phsC_TIME.c_str()); */
    bodyMin.Add("data",dataMin);
    neb::CJsonObject body;
    body.Add(bodyMax); 
    body.Add(bodyMin);  
    oJson.Add("body",body);    


    char pub_topic[256] = {0}; 
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//日月合格率
void CTaskManager::MqttPackNotificationData_5()
{
    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "4");    //日月合格率推送数据

    neb::CJsonObject bodyDay;   
    bodyDay.Add("type","0");//日合格率值
    bodyDay.Add("reporttime",jsheader.timestamp);//
    neb::CJsonObject dataDay; 
    JsonPackNameData(dataDay,"PhVPass_phsA","PhVPassDay_phsA");
    JsonPackNameData(dataDay,"PhVPass_phsB","PhVPassDay_phsB");
    JsonPackNameData(dataDay,"PhVPass_phsC","PhVPassDay_phsC");
    bodyDay.Add("data",dataDay);

    neb::CJsonObject bodyMon;   
    bodyMon.Add("type","1");//月合格率
    bodyDay.Add("reporttime",jsheader.timestamp);//

    neb::CJsonObject dataMon; 
    JsonPackNameData(dataMon,"PhVPass_phsA","PhVPassMon_phsA");
    JsonPackNameData(dataMon,"PhVPass_phsB","PhVPassMon_phsB");
    JsonPackNameData(dataMon,"PhVPass_phsC","PhVPassMon_phsC");
    bodyMon.Add("data",dataMon);
    

    neb::CJsonObject body;
    body.Add(bodyDay); 
    body.Add(bodyMon);  
    oJson.Add("body",body);    


    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }

}

//日月越限推送
void CTaskManager::MqttPackNotificationData_6()
{
     mqtt_header_s jsheader = {0}; 
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;
    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);
    oJson.Add("datatype", "5");    //日月越限推送

    neb::CJsonObject bodyDay;   
    bodyDay.Add("type","0");//日越限推送
    bodyDay.Add("reporttime",jsheader.timestamp);//

    neb::CJsonObject upperDataDay;   
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsA","PTOV_Rate_Day_phsA"); 
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsB","PTOV_Rate_Day_phsB");
    JsonPackNameData(upperDataDay,"PTOV_Rate_phsC","PTOV_Rate_Day_phsC");
    bodyDay.Add("upperData",upperDataDay);

    neb::CJsonObject lowerDataDay;   
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsA","PTUV_Rate_Day_phsA");
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsB","PTUV_Rate_Day_phsB");
    JsonPackNameData(lowerDataDay,"PTUV_Rate_phsC","PTUV_Rate_Day_phsC");
    bodyDay.Add("lowerData",lowerDataDay);


    
    neb::CJsonObject bodyMon;   
    bodyMon.Add("type","1");//月越限推送
    bodyMon.Add("reporttime",jsheader.timestamp);//
    neb::CJsonObject upperDataMon;   
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsA","PTOV_Rate_Mon_phsA");
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsB","PTOV_Rate_Mon_phsB");
    JsonPackNameData(upperDataMon,"PTOV_Rate_phsC","PTOV_Rate_Mon_phsC");
    bodyMon.Add("upperData",upperDataMon);

    neb::CJsonObject lowerDataMon;   
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsA","PTUV_Rate_Mon_phsA");
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsB","PTUV_Rate_Mon_phsB");
    JsonPackNameData(lowerDataMon,"PTUV_Rate_phsC","PTUV_Rate_Mon_phsC");
    bodyMon.Add("lowerData",lowerDataMon);
  
    neb::CJsonObject body;
    body.Add(bodyDay); 
    body.Add(bodyMon);  
    oJson.Add("body",body);    


    char pub_topic[256] = {0};
    snprintf(pub_topic, 128, CFG_APP_NAME"/Broadcast/JSON/report/notification/%s/%s",CParamManager::CreateInstance().m_devModel.c_str(), CParamManager::CreateInstance().m_devDev.c_str());
    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)(mssm.c_str()), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic, 256, strlen(pub_topic));
        item.msg_send_lenth = mssm.size();
        item.retained = 0;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
    }
}

//故障报告推送
void CTaskManager::MqttPackNotificationData_7()
{
}

bool CTaskManager::JsonPackNameData(neb::CJsonObject &obj, std::string name, std::string pdname, const char *timestamp)
{
    // 跳过初始值
    float fvlaue = CParamManager::CreateInstance().m_pdAnalyzer_value[pdname];
    if (fvlaue > 9998 || fvlaue < -9998)
    {
        return false;
    }

    neb::CJsonObject sondata;
    sondata.Add("name",name);  
    sondata.Add("id","1");
    sondata.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value[pdname])); 
    sondata.Add("timestamp", timestamp);
    obj.Add(sondata);
    return true;
}

bool CTaskManager::JsonPackNameData(neb::CJsonObject &obj, std::string name, std::string pdname)
{    
    neb::CJsonObject sondata;
    sondata.Add("name",name);  
    sondata.Add("id","1");
    sondata.Add("val",std::to_string(CParamManager::CreateInstance().m_pdAnalyzer_value[pdname]));     
    obj.Add(sondata);
    return true;
}

void CTaskManager::UnpackEsn(neb::CJsonObject obj)
{
    // bool bRet = true;
    neb::CJsonObject obj_body;
    if (!obj.Get("body", obj_body))
    {
        return;
    }

    if (obj_body.Get("serialNumber", m_esn))
    {
        mskprintf("serialNumber = %s.\n\r", m_esn.c_str());
        n_ops = OPS_INIT_OK;
    }
}

void CTaskManager::MqttPackOnline(CDataObj *obj)
{
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    mqtt_header_s jsheader = {0};
    int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
    if (ret != MQTT_OK)
    {
        MG_LOG_E("\nJson head fill failed.\n");
        return;
    }

    neb::CJsonObject oJson;

    oJson.Add("token", jsheader.token);
    oJson.Add("timestamp", jsheader.timestamp);

    neb::CJsonObject oBodyJson;
    oBodyJson.Add("state", "online");
    // obj->m_startTime
    C256String eventTime;
    STimeInfo st = ii_get_current_time();

    // eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
    //     obj->m_startTime.nYear,
    //     obj->m_startTime.nMonth,
    //     obj->m_startTime.nDay,
    //     obj->m_startTime.nHour,
    //     obj->m_startTime.nMinute,
    //     obj->m_startTime.nSecond);

    // 测试
    eventTime.Format("%04d-%02d-%02dT%02d:%02d:%02dZ",
                     st.nYear,
                     st.nMonth,
                     st.nDay,
                     st.nHour,
                     st.nMinute,
                     st.nSecond);

    oBodyJson.Add("event-time", eventTime.ToString()); // 第一次收到交采数据的
    oJson.Add("body", oBodyJson);

    ////mskprintf("ReadModelFile deviceType:%s.\n", obj->m_deviceType.c_str());
    // mskprintf("ReadModelFile manufacturerId:%s.\n", obj->m_manufacturerId.c_str());
    // mskprintf("ReadModelFile protocolType:%s.\n", obj->m_protocolType.c_str());
    // mskprintf("ReadModelFile manufacturerName:%s.\n", obj->m_manufacturerName.c_str());
    // mskprintf("ReadModelFile model:%s.\n", obj->m_model.c_str());
    // mskprintf("ReadModelFile topic:%s.\n", obj->m_topic.c_str());
    // mskprintf("ReadModelFile m_guid:%s.\n", obj->m_guid.guid.c_str());
    // mskprintf("ReadModelFile dev:%s.\n", obj->m_guid.dev.c_str());

    

    C256String pub_topic;
    pub_topic.Format("%s/notify/event/gwTerminal/status/%s/%s/%s/%s/%s/%s%s",
                     CFG_APP_NAME,
                     obj->m_manufacturerId.c_str(),
                     obj->m_manufacturerName.c_str(),
                     obj->m_deviceType.c_str(),
                     obj->m_model.c_str(),
                     obj->m_protocolType.c_str(),
                     obj->m_guid.dev.c_str(),
                     m_esn.c_str());

    std::string mssm = oJson.ToString();
    if (mssm.size() > 0)
    {
        mqtt_data_info_s item = {0};
        memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
        memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
        item.msg_send_lenth = mssm.size();
        item.retained = 1;
        CMqttClientInterManager::CreateInstance().Push_SendItem(item);
        // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 1);
    }
}

void CTaskManager::MqttPackData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    if (m_esn.empty())
    {
        return;
    }

    std::list<CNameObj>::iterator it = obj->m_SendNameObjs.begin();
    for (; it != obj->m_SendNameObjs.end(); it++)
    {
        bRet = false;
        CNameObj nameObj = *it;
        std::string serviceId = nameObj.m_serviceName;

        if (serviceId.compare("discrete") == 0 && CParamManager::CreateInstance().m_yxcycle == 0)
        {
            continue;
        }

        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        SMTimeInfo currenttime = ::ii_get_current_mtime();
        ret = snprintf(jsheader.timestamp, 32, "%04d-%02d-%02dT%02d:%02d:%02dZ",
                       currenttime.nYear,
                       currenttime.nMonth,
                       currenttime.nDay,
                       currenttime.nHour,
                       currenttime.nMinute,
                       0);

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
                // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
            }
        }

        // oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
        ii_sleep(100);
    }
}

// void CTaskManager::MqttPackData(CDataObj* obj)
//{
//     bool bRet = false;
//     if (obj == NULL) {
//         return;
//     }
//
//     std::map<std::string, CNameObj>::iterator it = obj->m_serviceIds.begin();
//     for (; it != obj->m_serviceIds.end(); it++)
//     {
//         std::string serviceId = it->first;
//         CNameObj nameObj = it->second;
//
//         if (serviceId.compare("discrete") == 0) {
//             continue;
//         }
//
//         mqtt_header_s jsheader = { 0 };
//         int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//         if (ret != MQTT_OK)
//         {
//             MG_LOG_E("\nJson head fill failed.\n");
//             return;
//         }
//
//         neb::CJsonObject oJson;
//
//         oJson.Add("token", jsheader.token);
//         oJson.Add("timestamp", jsheader.timestamp);
//
//         neb::CJsonObject oBodyJson;
//
//         //neb::CJsonObject oDataPropertiesJson;
//
//         oBodyJson.Add("serviceId", serviceId);
//         std::list<std::string>::iterator itName = nameObj.m_names.begin();
//         for (; itName != nameObj.m_names.end(); itName++)
//         {
//             std::string sname = *itName;
//             std::string svalue = obj->m_RealDatas[sname];
//             if (svalue.size() > 0) {
//                 oServicePropertiesJson.Add(sname, svalue);
//                 bRet = true;
//             }
//         }
//
//         oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//         oJson.Add("body", oBodyJson);
//         C256String pub_topic;
//         pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
//             CFG_APP_NAME,
//             obj->m_manufacturerId.c_str(),
//             obj->m_manufacturerName.c_str(),
//             obj->m_deviceType.c_str(),
//             obj->m_model.c_str(),
//             //obj->m_protocolType.c_str(),
//             obj->m_guid.dev.c_str(),
//             m_esn.c_str());
//
//         std::string mssm = oJson.ToString();
//         if (mssm.size() > 0 && bRet) {
//             mqtt_data_info_s item = { 0 };
//             memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char*)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
//             memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
//             item.msg_send_lenth = mssm.size();
//             item.retained = 0;
//             CMqttClientInterManager::CreateInstance().Push_SendItem(item);
//             //CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//         }
//         //oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//
//         ii_sleep(1000);
//     }
// }

void CTaskManager::MqttPackSpontData(CDataObj *obj)
{
    bool bRet = false;
    if (obj == NULL)
    {
        return;
    }

    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
    {
        std::string serviceId = obj->m_SpontObj.m_serviceName;
        CNameObj nameObj = obj->m_SpontObj;
        mqtt_header_s jsheader = {0};
        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
        if (ret != MQTT_OK)
        {
            MG_LOG_E("\nJson head fill failed.\n");
            return;
        }

        neb::CJsonObject oJson;

        oJson.Add("token", jsheader.token);
        oJson.Add("timestamp", jsheader.timestamp);

        neb::CJsonObject oBodyJson;
        neb::CJsonObject oServicePropertiesJson;
        // neb::CJsonObject oDataPropertiesJson;

        oBodyJson.Add("serviceId", serviceId);
        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
        for (; itName != nameObj.m_Spontnames.end(); itName++)
        {
            std::string sname = *itName;
            std::string svalue = obj->m_RealDatas[sname];
            if (svalue.size() > 0)
            {
                oServicePropertiesJson.Add(sname, svalue);
                bRet = true;
            }
        }
        if (bRet)
        {
            // oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
            oBodyJson.Add("serviceProperties", oServicePropertiesJson);
            oJson.Add("body", oBodyJson);
            C256String pub_topic;
            pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s%s",
                             CFG_APP_NAME,
                             obj->m_manufacturerId.c_str(),
                             obj->m_manufacturerName.c_str(),
                             obj->m_deviceType.c_str(),
                             obj->m_model.c_str(),
                             // obj->m_protocolType.c_str(),
                             obj->m_guid.dev.c_str(),
                             m_esn.c_str());

            std::string mssm = oJson.ToString();
            if (mssm.size() > 0 && bRet)
            {
                mqtt_data_info_s item = {0};
                memcpy_safe(item.msg_send, MSG_ARRVD_MAX_LEN, (char *)mssm.c_str(), MSG_ARRVD_MAX_LEN, mssm.size());
                memcpy_safe(item.pubtopic, 256, (char *)pub_topic.ToChar(), 256, pub_topic.Length());
                item.msg_send_lenth = mssm.size();
                item.retained = 0;
                CMqttClientInterManager::CreateInstance().Push_SendItem(item);
                // CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
            }
        }
        ii_sleep(1000);
    }
}

bool CTaskManager::IsFindDiscrete(CDataObj *obj, std::string name, std::string &serveiceName)
{
    bool bRet = false;
    std::string serviceDis = "discrete";
    std::string serviceDis1 = "yx";
    std::string serviceDis2 = "YX";

    std::map<std::string, CNameObj>::iterator iter = obj->m_serviceIds.find(serviceDis);
    if (iter != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis;
                return true;
            }
        }
    }
    

    std::map<std::string, CNameObj>::iterator iter1 = obj->m_serviceIds.find(serviceDis1);
    if (iter1 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis1];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis1;
                return true;
            }
        }
    }

    std::map<std::string, CNameObj>::iterator iter2 = obj->m_serviceIds.find(serviceDis2);
    if (iter2 != obj->m_serviceIds.end())
    {
        CNameObj nameObj = obj->m_serviceIds[serviceDis2];
        std::list<std::string>::iterator itName = nameObj.m_names.begin();
        for (; itName != nameObj.m_names.end(); itName++)
        {
            std::string sname = *itName;
            if (sname.compare(name.c_str()) == 0)
            {
                serveiceName = serviceDis2;
                return true;
            }
        }
    }

    return bRet;
}

void CTaskManager::pdAnalyzer_analysis()
{ 
    // 按分钟计算电能质量分析数据
    SMTimeInfo tm = ii_get_current_mtime();
    pdAnalyzer_get_time_change(tm);
    static int cnt = 1;
    if(cnt == 1)
    {
        cnt++;
        m_lastTime = tm;
    }
}

int CTaskManager::pdAnalyzer_get_time_change(SMTimeInfo now_time)
{
    //计算周
    int y = m_lastTime.nYear % 100;
    int c = m_lastTime.nYear / 100;
    int m = m_lastTime.nMonth;

    if (m < 3)
    {
        m += 12;
    }
    int d = m_lastTime.nDay;
    Juint8 week = y + y / 4 + c / 4 - 2 * c + 26 * (m + 1) / 10 + d - 1;
    week = week % 7;
    m_lastWeek = week;
    mskprintf("%04d-%02d-%02d %02d:%02d:%02d:%03d week:%d\n",now_time.nYear,now_time.nMonth,now_time.nDay,now_time.nHour,now_time.nMinute,now_time.nSecond,now_time.nMSecond,m_lastWeek);
   
    char calTime[30];
    snprintf(calTime,29,"%04d-%02d-%02dT%02d:%02d:%02d.%03d+0800", now_time.nYear,now_time.nMonth,now_time.nDay,now_time.nHour,now_time.nMinute,now_time.nSecond,now_time.nMSecond);
     
    std::string str_time = calTime;

    //mskprintf("%d--%s\n",m_minute_start,str_time.c_str());
    
    

    CParamManager::CreateInstance().judge_m_TCalOvertime();//电荷不平衡度YX;
    CParamManager::CreateInstance().voltage_yx_analysis();//遥信事件判断。
    //CParamManager::CreateInstance().voltage_yx_analysis_ADC(); //遥信事件判断
    



    if (m_minute_start)
    {
        // 计算越限时间
        time_t time_val = time(NULL);
        float interval = 0;
        if (m_lastsec == 0)
        {
            m_lastsec = time_val;
        }
        else
        {
            interval = time_val - m_lastsec;
            m_lastsec = time_val;
        }
        CParamManager::CreateInstance().voltage_limit_analysis(interval,str_time);
    }

    //mskprintf("%d--%d\n",m_lastTime.nMinute,now_time.nMinute);

    //分钟有变化 
    if(m_lastTime.nMinute != now_time.nMinute)
    {
        m_lastTime.nMinute = now_time.nMinute;
        if(m_minute_start)
        {
            if(CParamManager::CreateInstance().Unpack_pdAnalyzerData())//一分钟计算一次电能质量，周期参数        
            {
                //计算完发送一次数据
                // 1、实时数据写所有的数据进行写到数据库
                MqttPackSetRealTimeData();
                // 2、数据变化上报，104接收；2.1 遥测推送，
                MqttPackNotificationData_1();

                MqttPack_pdAnalyzerData();      //测试用 
            }   

            if(m_lastTime.nMinute % 15 == 0 )//每15分钟发送一次上日上月信息，极值
            {
                MqttPack_pdAnalyzerData();
            }
        }         

        if(!m_minute_start && now_time.nSecond == 0)
        {
            m_minute_start = true;//作为开始计时标志  避免第一次上电多计数   
            m_ocAnalyzer_T.StartCount();        //同时开始计算可开放容量分析 
			m_ocLoadRate_T.StartCount();        //同时计算配变负载率
        }
    }

    if(m_lastTime.nDay != now_time.nDay) //日变化 
    {
        m_lastTime.nDay = now_time.nDay;  
        //初始化
        CParamManager::CreateInstance().initDayData();

        //一周结束    
        int y = m_lastTime.nYear%100;
        int c = m_lastTime.nYear/100;
        int m = m_lastTime.nMonth;
        if(m <3)
        {
            m+=12;
        }
        int d = m_lastTime.nDay;
        Juint8 week = y+y/4+c/4-2*c+26*(m+1)/10+d-1;
        week = week%7;

        mskprintf("m_lastWeek : %d ,week : %d\n",m_lastWeek, week);
        //周变化
        if(m_lastWeek != week)
        {
            m_lastWeek = week;
            CParamManager::CreateInstance().initWeekData();
        }
    }

    //mskprintf("m_lastTime.nMonth : %d ,now_time.nMonth : %d\n",m_lastTime.nMonth,now_time.nMonth);
    if(m_lastTime.nMonth != now_time.nMonth) //月变化 
    {
        m_lastTime.nMonth = now_time.nMonth;
        CParamManager::CreateInstance().initMonData();
    }

    return 0;
}

void CTaskManager::CalMachinecode(char *str, char *currentCode)
{
     int RecvBuff[4096];
    memset(RecvBuff, 0, sizeof(RecvBuff));
    int nCount = 0;

    // 将机器码字符串转换为整数数组
    for (int i = 0; i < strlen(str); i += 2)
    {
        char str2[3] = {str[i], str[i + 1], '\0'};
        int strtxt = (int)strtol(str2, NULL, 16);
        RecvBuff[nCount] = strtxt;
        nCount++;
    }

    // 根据机器码计算注册码
    int m;
    for (int i = 0; i < nCount; i++)
    {
        int n = RecvBuff[i];
        if (n == 0x10)
        {
            m = 0xFF;
            sprintf(currentCode, "%s%02X%s", currentCode, m, "FF");
        }
        else if (n % 5 == 3)
        {
            m = n + 2;
            sprintf(currentCode, "%s%02X%s", currentCode, m, "MEG");
        }
        else
        {
            m = n - 1;
            if (m < 0)
            {
                m = 0;
                sprintf(currentCode, "%s%02X%s", currentCode, m, "10");
            }
            else if (m % 3 == 1)
            {
                sprintf(currentCode, "%s%02X%s", currentCode, m, "A#");
            }
            else
            {
                sprintf(currentCode, "%s%02X%s", currentCode, m, "C$");
            }
        }
    }
}

void CTaskManager::GetFileNames(string path, vector<string> &filenames)
{
    DIR *pDir;
    struct dirent *ptr;
    if (!(pDir = opendir(path.c_str())))
        return;
    while ((ptr = readdir(pDir)) != 0)
    {
        if (strcmp(ptr->d_name, ".") != 0 && strcmp(ptr->d_name, "..") != 0)
            filenames.push_back(path + "/" + ptr->d_name);
    }
    closedir(pDir);
}

std::string CTaskManager::GetFirsAppName(std::string topic)
{
    std::string str;
    size_t index = topic.find_first_of("/", 0);
    if (index != std::string::npos)
    {
        str = topic.substr(0, index);
    }
    return str;
}

bool CTaskManager::GetStringVlaue(neb::CJsonObject obj, const char *key, C64String &str)
{
    std::string s;
    if (obj.Get(key, s))
    {
        str = s;
        return true;
    }
    else
    {
        return false;
    }
}

std::string CTaskManager::CreatMd5(std::string str)
{
    MD5_CTX md5;
    std::string out;
    unsigned char md[16] = {0};

    if (!MD5_Init(&md5))
    {
        mskprintf("MD5_Init error\n");
        return "";
    }
    if (!MD5_Update(&md5, str.c_str(), strlen(str.c_str())))
    {
        mskprintf("MD5_Update error\n");
        return "";
    }
    if (!MD5_Final(md, &md5))
    {
        mskprintf("MD5_Final error\n");
        return "";
    }
    for (int i = 0; i < 16; i++)
    {
        char outc[3] = {0};
        sprintf(outc, "%02X", md[i]);
        std::string outb = outc;
        out = out + outb;
    }
    return out;
}
CTaskManager::CTaskManager()
{
    m_Thread.SetPackageAcquireManager(this);
    m_T.SetParam(120 * 1000); // 默认120秒
    m_T.StartCount();
    m_ESN_T.SetParam(600 * 1000); // 默认600秒
    m_ESN_T.StartCount();
    m_Data_T.SetParam(30 * 1000); //
    m_Data_T.StartCount();

    mskprintf("电能质量分析m_Stat_Interval_t: %d\n",CParamManager::CreateInstance().m_Stat_Interval_t);
    if(CParamManager::CreateInstance().m_Stat_Interval_t < 1)
    {
        m_pdAnalyzer_T.SetParam(1000*1); // 电能质量分析
    }
    else
    {
        m_pdAnalyzer_T.SetParam(1000*CParamManager::CreateInstance().m_Stat_Interval_t); // 电能质量分析
    }
    
    m_ocAnalyzer_T.SetParam(1000 *60); // 分钟为单位  可开放容量分析     
	m_ocLoadRate_T.SetParam(1000 * 10);  //10秒计算一次
    m_minute_start = false;
    m_lastsec = 0;
    m_lastWeek = 0;
}

CTaskManager::~CTaskManager()
{
    std::list<CDataObj *>::iterator iter = m_dataObj.begin();
    for (; iter != m_dataObj.end(); ++iter)
    {
        CDataObj *obj = *iter;
        if (obj != NULL)
        {
            delete obj;
            obj = NULL;
        }
    }
}

//
// void CTaskManager::MqttPackData(CDataObj* obj)
//{
//    bool bRet = false;
//    if (obj == NULL) {
//        return;
//    }
//
//    std::map<std::string, CNameObj>::iterator it = obj->m_serviceIds.begin();
//    for (; it != obj->m_serviceIds.end(); it++)
//    {
//        std::string serviceId = it->first;
//        CNameObj nameObj = it->second;
//
//        if (serviceId.compare("discrete") == 0) {
//            continue;
//        }
//
//        mqtt_header_s jsheader = { 0 };
//        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//        if (ret != MQTT_OK)
//        {
//            MG_LOG_E("\nJson head fill failed.\n");
//            return;
//        }
//
//        neb::CJsonObject oJson;
//
//        oJson.Add("token", jsheader.token);
//        oJson.Add("timestamp", jsheader.timestamp);
//
//        neb::CJsonObject oBodyJson;
//        neb::CJsonObject oServicePropertiesJson;
//        neb::CJsonObject oDataPropertiesJson;
//
//        oBodyJson.Add("serviceId", serviceId);
//        std::list<std::string>::iterator itName = nameObj.m_names.begin();
//        for (; itName != nameObj.m_names.end(); itName++)
//        {
//            std::string sname = *itName;
//            std::string svalue = obj->m_RealDatas[sname];
//            if (svalue.size() > 0) {
//                oDataPropertiesJson.Add(sname, svalue);
//                bRet = true;
//            }
//        }
//
//        oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//        oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//        oJson.Add("body", oBodyJson);
//        C256String pub_topic;
//        pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s/%s%s",
//            CFG_APP_NAME,
//            obj->m_manufacturerId.c_str(),
//            obj->m_manufacturerName.c_str(),
//            obj->m_deviceType.c_str(),
//            obj->m_model.c_str(),
//            obj->m_protocolType.c_str(),
//            obj->m_guid.dev.c_str(),
//            m_esn.c_str());
//
//        std::string mssm = oJson.ToString();
//        if (mssm.size() > 0 && bRet) {
//            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//        }
//        ii_sleep(1000);
//    }
//}
//
// void CTaskManager::MqttPackSpontData(CDataObj* obj)
//{
//    bool bRet = false;
//    if (obj == NULL) {
//        return;
//    }
//
//    std::list<std::string>::iterator it = obj->m_SpontObj.m_Spontnames.begin();
//    for (; it != obj->m_SpontObj.m_Spontnames.end(); it++)
//    {
//        std::string serviceId = obj->m_SpontObj.m_serviceName;
//        CNameObj nameObj = obj->m_SpontObj;
//        mqtt_header_s jsheader = { 0 };
//        int ret = CMqttClientInterManager::CreateInstance().MqttHeaderFill(&jsheader, NULL);
//        if (ret != MQTT_OK)
//        {
//            MG_LOG_E("\nJson head fill failed.\n");
//            return;
//        }
//
//        neb::CJsonObject oJson;
//
//        oJson.Add("token", jsheader.token);
//        oJson.Add("timestamp", jsheader.timestamp);
//
//        neb::CJsonObject oBodyJson;
//        neb::CJsonObject oServicePropertiesJson;
//        neb::CJsonObject oDataPropertiesJson;
//
//        oBodyJson.Add("serviceId", serviceId);
//        std::list<std::string>::iterator itName = nameObj.m_Spontnames.begin();
//        for (; itName != nameObj.m_Spontnames.end(); itName++)
//        {
//            std::string sname = *itName;
//            std::string svalue = obj->m_RealDatas[sname];
//            if (svalue.size() > 0) {
//                oDataPropertiesJson.Add(sname, svalue);
//                bRet = true;
//            }
//        }
//
//        oServicePropertiesJson.Add("dataProperties", oDataPropertiesJson);
//        oBodyJson.Add("serviceProperties", oServicePropertiesJson);
//        oJson.Add("body", oBodyJson);
//        C256String pub_topic;
//        pub_topic.Format("%s/terminal/dataReport/%s/%s/%s/%s/%s/%s%s",
//            CFG_APP_NAME,
//            obj->m_manufacturerId.c_str(),
//            obj->m_manufacturerName.c_str(),
//            obj->m_deviceType.c_str(),
//            obj->m_model.c_str(),
//            obj->m_protocolType.c_str(),
//            obj->m_guid.dev.c_str(),
//            m_esn.c_str());
//
//        std::string mssm = oJson.ToString();
//        if (mssm.size() > 0 && bRet) {
//            CMqttClientInterManager::CreateInstance().agent_MqttMsgPub((char*)(mssm.c_str()), (char *)pub_topic.ToChar(), 0);
//        }
//        ii_sleep(1000);
//    }
//}

CNameObj::CNameObj()
{
}

CNameObj::~CNameObj()
{
}

